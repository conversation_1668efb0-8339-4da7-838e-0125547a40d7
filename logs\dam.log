07-24 09:24:21.369 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-24 09:24:21.375 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 13444 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-24 09:24:21.376 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-24 09:24:22.136 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-24 09:24:22.137 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-24 09:24:22.165 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
07-24 09:24:22.440 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=1b44a730-e903-3697-99d9-e7814c6b20b7
07-24 09:24:22.791 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-24 09:24:22.801 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-24 09:24:22.801 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-24 09:24:22.801 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-24 09:24:22.988 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-24 09:24:22.989 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1576 ms
07-24 09:24:23.029 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-24 09:24:23.098 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-24 09:24:23.621 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-24 09:24:25.241 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-24 09:24:25.251 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-24 09:24:25.251 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-24 09:24:25.252 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-24 09:24:25.252 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-24 09:24:25.252 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-24 09:24:25.252 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-24 09:24:25.252 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@62c1259f
07-24 09:24:25.480 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-24 09:24:25.547 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-24 09:24:25.592 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-24 09:24:25.609 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-24 09:24:25.782 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-24 09:24:25.782 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-24 09:24:25.792 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 6.034 seconds (JVM running for 7.33)
07-24 09:25:00.085 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:25:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:25:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:25:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:26:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:26:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:26:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:26:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:27:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:27:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:27:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:27:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:28:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:28:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:28:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:28:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:28:04.706 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-24 09:28:04.847 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-24 09:28:04.847 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-24 09:28:04.847 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-24 09:28:04.848 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-24 09:28:04.878 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-24 09:28:05.125 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-24 09:28:09.733 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-24 09:28:09.736 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 21220 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-24 09:28:09.737 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-24 09:28:10.360 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-24 09:28:10.361 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-24 09:28:10.379 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-24 09:28:10.528 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=1b44a730-e903-3697-99d9-e7814c6b20b7
07-24 09:28:10.792 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-24 09:28:10.799 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-24 09:28:10.799 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-24 09:28:10.800 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-24 09:28:10.915 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-24 09:28:10.915 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1154 ms
07-24 09:28:10.946 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-24 09:28:10.993 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-24 09:28:11.544 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-24 09:28:13.052 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-24 09:28:13.058 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-24 09:28:13.058 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-24 09:28:13.058 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-24 09:28:13.058 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-24 09:28:13.058 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-24 09:28:13.059 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-24 09:28:13.059 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@139b3a29
07-24 09:28:13.242 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-24 09:28:13.284 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-24 09:28:13.342 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-24 09:28:13.363 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-24 09:28:13.530 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-24 09:28:13.534 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-24 09:28:13.543 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.277 seconds (JVM running for 5.954)
07-24 09:29:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:29:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:29:00.171 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:29:00.201 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:30:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:30:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:30:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:30:00.179 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:31:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:31:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:31:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:31:00.186 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:31:17.650 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-24 09:31:17.650 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-24 09:31:17.652 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
07-24 09:31:26.272 [http-nio-20000-exec-4] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [229] milliseconds.
07-24 09:32:00.089 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:32:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:32:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:32:00.179 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:33:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:33:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:33:00.158 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:33:00.187 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:34:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:34:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:34:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:34:00.203 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:35:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:35:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:35:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:35:00.185 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:36:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:36:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:36:00.178 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:36:00.208 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:37:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:37:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:37:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:37:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:38:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:38:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:38:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:38:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:39:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:39:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:39:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:39:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:40:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:40:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:40:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:40:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:41:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:41:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:41:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:41:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:42:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:42:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:42:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:42:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:43:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:43:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:43:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:43:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:44:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:44:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:44:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:44:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:44:00.741 [http-nio-20000-exec-3] WARN  org.springframework.web.servlet.PageNotFound - No mapping for POST /api/app/config
07-24 09:44:01.978 [http-nio-20000-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for POST /api/app/config
07-24 09:45:00.067 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:45:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:45:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:45:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:46:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:46:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:46:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:46:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:47:00.067 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:47:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:47:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:47:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:48:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:48:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:48:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:48:00.161 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:49:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:49:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:49:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:49:00.168 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-24 09:49:26.219 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-24 09:49:26.410 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-24 09:49:26.411 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-24 09:49:26.411 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-24 09:49:26.411 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-24 09:49:26.555 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-24 09:49:26.861 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
