<template>
  <div>
    <el-dialog v-dialog-drag title="退库申请" width="900px" :visible.sync="visible" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form ref="dataform" size="small" label-width="140px" :model="form" :rules="formRules">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="退库日期：" prop="time">
              <el-date-picker v-model="form.time" type="date" placeholder="请选择退库日期" value-format="yyyy-MM-dd" format="yyyy-MM-dd" clearable editable style="width:100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退库人：" prop="user">
              <user-chosen v-model="form.user" type="1" clearable placeholder="请选择退库人" style="width:100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="退至部门/区域：" prop="region">
              <dept-region-chosen v-model="deptRegion" :simple="false" clearable placeholder="请选择退至部门/区域" style="width:100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退库说明：" prop="memo">
              <el-input v-model="form.memo" maxlength="200" show-word-limit clearable placeholder="请输入退库说明" style="width:100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <upload-file v-model="fileList" simple multiple type="TK" />
      <div style="margin-top:20px;">退库资产明细：</div>
      <el-divider></el-divider>
      <el-table :data="assetList" size="small" border>
        <el-table-column label="资产类型" prop="typeName" width="180" header-align="center" fixed="left" />
        <el-table-column label="资产编码" prop="no" width="120" align="center" fixed="left" />
        <el-table-column label="资产名称" prop="name" min-width="150" fixed="left" />
        <el-table-column label="所属部门" prop="deptName" width="150" header-align="center" />
        <el-table-column label="当前状态" prop="status" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="getAssetStatusType(scope.row)" size="small">{{ getAssetStatusText(scope.row) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="80">
          <template slot="header">
            <el-button type="success" size="mini" @click="addAsset">新 增</el-button>
          </template>
          <template slot-scope="scope">
            <el-link type="danger" size="mini" icon="el-icon-remove" @click.stop="removeAsset(scope.index)">移除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <asset-chosen ref="asset" multiple @selected="selectedAsset" />
  </div>
</template>
<script>
import store from '@/store'
import { dateStr } from '@/utils'
import { getAssetStatusType, getAssetStatusText } from '../js/asset.js'

import AssetChosen from '../account/AssetChosen.vue'
import UserChosen from '@/views/components/UserChosen.vue'
import DeptRegionChosen from '@/views/components/DeptRegionChosen.vue'
import UploadFile from '@/views/components/UploadFile.vue'

export default {
  components: { AssetChosen, UserChosen, DeptRegionChosen, UploadFile },
  data() {
    const deptRegionRule = (rule, value, callback) => {
      if (!this.form.dept) callback('请选择退至部门/区域')
      else callback()
    }
    return {
      visible: false,
      currDate: dateStr(),
      form: { user: null }, // 退库申请信息表单
      deptRegion: [],
      formRules: {
        time: [{ required: true, message: '请选择退库日期', trigger: 'blur' }],
        user: [{ required: true, message: '请选择退库人', trigger: 'blur' }],
        region: [{ validator: deptRegionRule, trigger: 'blur' }]
      },
      assetList: [],
      fileList: []
    }
  },
  watch: {
    deptRegion: function(nv) {
      this.form.dept = Array.isArray(nv) && nv.length > 0 ? nv[nv.length - 1] : null
    }
  },
  methods: {
    getAssetStatusType(v) {
      return getAssetStatusType(v.status)
    },
    getAssetStatusText(v) {
      return getAssetStatusText(v.status)
    },
    show() {
      this.visible = true
      this.form = { time: dateStr(), user: store.getters.user.id }
      this.assetList = []
      this.fileList = []
    },
    addAsset() {
      this.$refs.asset.show({ status: '2' })
    },
    selectedAsset(items) {
      const ids = { }
      this.assetList.forEach(r => { ids[r.id] = true })
      items.forEach(r => {
        if (!ids[r.id]) this.assetList.push(r)
      })
    },
    removeAsset(index) {
      this.$confirm('确定要移除吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        this.assetList.splice(index, 1)
      }).catch(() => {
      })
    },
    save() {
      this.$refs.dataform.validate(valid => {
        if (valid) {
          const details = []
          this.assetList.forEach(r => details.push({ asset: r.id, dept: r.dept, region: r.region, location: r.location, useDept: r.useDept, useUser: r.useUser, assetStatus: r.status, lastLng: r.lng, lastLat: r.lat, lastLocAddr: r.locAddr }))
          if (!details.length) return this.$message.warning('请选择要退库的资产')
          this.form.assetList = details
          this.form.fileList = this.fileList
          this.$http({ url: '/am/asset/back/apply', data: this.form }).then(res => {
            if (res.code > 0) {
              this.visible = false
              this.$message.success('提交成功')
              this.$emit('success')
            }
          })
        }
      })
    }
  }
}
</script>
