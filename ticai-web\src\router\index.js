import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login')
  },
  {
    path: '/404',
    component: () => import('@/views/404')
  },
  {
    path: '/formDesigner',
    meta: { title: '表单设置' },
    component: () => import('@/views/form/designer')
  },
  {
    path: '/formSimple',
    meta: { title: '表单设置' },
    component: () => import('@/views/form/simple')
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      meta: { name: '首页' },
      component: () => import('@/views/dashboard')
    }]
  },
  {
    path: '/main',
    component: Layout,
    children: [
      {
        path: 'profile',
        meta: { name: '个人中心' },
        component: () => import('@/views/main/profile')
      },
      {
        path: 'qa',
        meta: { name: '问题' },
        component: () => import('@/views/main/qa')
      },
      {
        path: 'icon',
        meta: { name: '图标' },
        component: () => import('@/views/main/icon')
      }
    ]
  },
  {
    path: '/sys', // 系统管理
    component: Layout,
    children: [
      {
        path: 'dept',
        meta: { name: '组织机构' },
        component: () => import('@/views/sys/dept')
      },
      {
        path: 'role',
        meta: { name: '角色管理' },
        component: () => import('@/views/sys/role')
      },
      {
        path: 'user',
        meta: { name: '用户管理' },
        component: () => import('@/views/sys/user')
      },
      {
        path: 'dict',
        meta: { name: '字典管理' },
        component: () => import('@/views/sys/dict')
      },
      {
        path: 'region',
        meta: { name: '区划编码' },
        component: () => import('@/views/sys/region')
      },
      {
        path: 'log',
        meta: { name: '日志管理' },
        component: () => import('@/views/sys/log')
      },
      {
        path: 'port',
        meta: { name: '接口应用' },
        component: () => import('@/views/sys/port')
      }
    ]
  },
  {
    path: '/asset', // 资产信息
    component: Layout,
    children: [
      {
        path: 'type',
        meta: { name: '资产类型' },
        component: () => import('@/views/asset/type')
      },
      {
        path: 'account',
        meta: { name: '资产台账' },
        component: () => import('@/views/asset/account')
      },
      {
        path: 'allocate',
        meta: { name: '资产调拨' },
        component: () => import('@/views/asset/allocate')
      },
      {
        path: 'consuming',
        meta: { name: '资产领用' },
        component: () => import('@/views/asset/consuming')
      },
      {
        path: 'deposit',
        meta: { name: '资产押金' },
        component: () => import('@/views/asset/deposit')
      },
      {
        path: 'transfer',
        meta: { name: '资产移交' },
        component: () => import('@/views/asset/transfer')
      },
      {
        path: 'change',
        meta: { name: '资产变更' },
        component: () => import('@/views/asset/change')
      },
      {
        path: 'inventory',
        meta: { name: '资产盘点' },
        component: () => import('@/views/asset/inventory')
      },
      {
        path: 'back',
        meta: { name: '资产退库' },
        component: () => import('@/views/asset/back')
      },
      {
        path: 'maintain',
        meta: { name: '维修登记' },
        component: () => import('@/views/asset/maintain')
      },
      {
        path: 'scrap',
        meta: { name: '清理报废' },
        component: () => import('@/views/asset/scrap')
      },
      {
        path: 'asset-trace',
        meta: { name: '设备管理查询' },
        component: () => import('@/views/asset/asset-trace')
      },
      {
        path: 'asset-trace',
        meta: { name: '资产管理查询' },
        component: () => import('@/views/asset/asset-trace')
      }
    ]
  },
  {
    path: '/patrol', // 巡检
    component: Layout,
    children: [
      {
        path: 'kpi',
        meta: { name: '巡检指标' },
        component: () => import('@/views/patrol/kpi')
      },
      {
        path: 'point',
        meta: { name: '巡检点位' },
        component: () => import('@/views/patrol/point')
      },
      {
        path: 'asset',
        meta: { name: '巡检资产' },
        component: () => import('@/views/patrol/asset')
      },
      {
        path: 'path',
        meta: { name: '巡检路线' },
        component: () => import('@/views/patrol/path')
      },
      {
        path: 'plan',
        meta: { name: '巡检计划' },
        component: () => import('@/views/patrol/plan')
      },
      {
        path: 'check',
        meta: { name: '终端设备自检' },
        component: () => import('@/views/patrol/check')
      },
      {
        path: 'task',
        meta: { name: '巡检执行' },
        component: () => import('@/views/patrol/task')
      },
      {
        path: 'abnormal',
        meta: { name: '巡检异常' },
        component: () => import('@/views/patrol/abnormal')
      }
    ]
  },
  {
    path: '/bd', // 基础管理
    component: Layout,
    children: [
      {
        path: 'no',
        meta: { name: '业务编码' },
        component: () => import('@/views/bd/no')
      },
      {
        path: 'region',
        meta: { name: '区域网点' },
        component: () => import('@/views/bd/region')
      },
      {
        path: 'maintain',
        meta: { name: '维保单位' },
        component: () => import('@/views/bd/maintain')
      },
      {
        path: 'sms',
        meta: { name: '短信服务' },
        component: () => import('@/views/bd/sms')
      }
    ]
  },
  {
    path: '/dp', // 大屏
    component: () => import('@/views/dp'),
    redirect: '/dp/home',
    children: [
      {
        name: 'DpHome',
        path: 'home',
        meta: { name: '大屏首页' },
        component: () => import('@/views/dp/home')
      },
      {
        name: 'DpAsset',
        path: 'asset',
        meta: { name: '资产设备' },
        component: () => import('@/views/dp/asset')
      },
      {
        name: 'DpDistr',
        path: 'distri',
        meta: { name: '资产分布' },
        component: () => import('@/views/dp/distri')
      },
      {
        name: 'DpPoint',
        path: 'point',
        meta: { name: '点位分布' },
        component: () => import('@/views/dp/point')
      },
      {
        name: 'DpPatrol',
        path: 'patrol',
        meta: { name: '巡检统计' },
        component: () => import('@/views/dp/patrol')
      },
      {
        name: 'DpWo',
        path: 'wo',
        meta: { name: '工单统计' },
        component: () => import('@/views/dp/wo')
      },
      {
        name: 'DpMonitor',
        path: 'monitor',
        meta: { name: '实时监控' },
        component: () => import('@/views/dp/monitor')
      }
    ]
  },
  {
    path: '/wx', // 微信管理
    component: Layout,
    children: [
      {
        path: 'banner',
        meta: { name: '微信横幅' },
        component: () => import('@/views/wx/banner')
      },
      {
        path: 'user',
        meta: { name: '微信用户' },
        component: () => import('@/views/wx/user')
      }
    ]
  },
  {
    path: '/rp', // 报表
    component: Layout,
    children: [
      {
        path: 'asset/info',
        meta: { name: '资产信息统计' },
        component: () => import('@/views/rp/asset/info')
      },
      {
        path: 'asset/biz',
        meta: { name: '资产增变退统计' },
        component: () => import('@/views/rp/asset/biz')
      },
      {
        path: 'wo/abnormal',
        meta: { name: '故障维护统计' },
        component: () => import('@/views/rp/wo/abnormal')
      },
      {
        path: 'wo/biz',
        meta: { name: '工单统计' },
        component: () => import('@/views/rp/wo/biz')
      },
      {
        path: 'asset/sn',
        meta: { name: '资产终端机绑定统计' },
        component: () => import('@/views/rp/asset/sn')
      },
      {
        path: 'asset/scan',
        meta: { name: '扫码定位统计' },
        component: () => import('@/views/rp/asset/scan')
      }
    ]
  },
  {
    path: '/wo', // 工单服务
    component: Layout,
    children: [
      {
        path: 'index',
        meta: { name: '工单管理' },
        component: () => import('@/views/wo/index')
      },
      {
        path: 'confirm',
        meta: { name: '工单管理' },
        component: () => import('@/views/wo/confirm')
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
