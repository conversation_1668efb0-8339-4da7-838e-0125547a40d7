<template>
  <div class="layout-lr">
    <div class="left">
      <div class="head">区域列表</div>
      <div class="body">
        <ul>
          <li :class="{ act: activeItem == null || activeItem.id == null }" @click="showRegion({})">所有区域</li>
          <li v-for="item in regionList" :key="item.id" :class="{ act: activeItem && activeItem.id == item.id }"
            @click="showRegion(item)">{{ item.name }}</li>
        </ul>
      </div>
    </div>
    <div class="center">
      <el-descriptions title="区域信息" :column="3" border class="descr-3">
        <template slot="extra">
          <div class="button-bar">
            <el-button type="primary" size="mini" icon="el-icon-plus" @click="addRegion">新增区域</el-button>
            <el-button type="primary" size="mini" icon="el-icon-upload" @click="upload">网点导入</el-button>
            <template v-if="activeItem && activeItem.id">
              <el-button type="primary" size="mini" icon="el-icon-edit" @click="editRegion">编辑区域</el-button>
              <el-button type="danger" size="mini" icon="el-icon-remove" @click="removeRegion">删除区域</el-button>
              <el-button type="success" size="mini" icon="el-icon-plus" @click="addLocation">新增网点</el-button>
              <el-dropdown @command="handleBatchCommand" style="margin-left:10px">
                <el-button type="warning" size="mini">
                  批量修改<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="export">导出</el-dropdown-item>
                  <el-dropdown-item command="import">导入</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </div>
        </template>
        <template v-if="activeItem && activeItem.id">
          <el-descriptions-item label="所属部门">{{ activeItem.deptName }}</el-descriptions-item>
          <el-descriptions-item label="区域代码">{{ activeItem.code }}</el-descriptions-item>
          <el-descriptions-item label="区域名称">{{ activeItem.name }}</el-descriptions-item>
          <el-descriptions-item label="所在区划">{{ activeItem.regionName }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ activeItem.userName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ activeItem.userPhone }}</el-descriptions-item>
          <el-descriptions-item :span="3" label="区域范围">{{ activeItem.scope }}</el-descriptions-item>
        </template>
      </el-descriptions>
      <el-divider></el-divider>
      <div class="location-head">
        <span class="location-title">区域网点</span>
        <div class="location-search">
          <el-input v-model="qform.keyword" clearable size="mini" placeholder="输入关键字" autocomplete="off">
            <template slot="prepend">检索:</template>
            <el-button slot="append" icon="el-icon-search" @click="searchLocation"></el-button>
          </el-input>
        </div>
      </div>
      <page-table ref="grid" size="mini" path="/am/location/page" :query="qform" stripe border>
        <el-table-column v-if="activeItem == null || activeItem.id == null" label="所属区域" prop="regionName" width="100"
          align="center" />
        <el-table-column label="网点编码" prop="code" width="110" align="center" />
        <el-table-column label="网点名称" prop="name" width="300" align="center" />
        <el-table-column label="负责人" prop="contact" width="300" align="center" />
        <el-table-column label="联系方式" prop="phone" width="150" align="center" />
        <el-table-column label="地址" prop="address" width="800" align="center" />
        <el-table-column label="网点时间" prop="createTime" align="center" width="150">
          <template slot-scope="scope">
            {{ scope.row.createTime ? formatDateTime(scope.row.createTime) : '' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template slot-scope="{ row }">
            <div v-if="row.id != 'admin'">
              <el-button type="primary" size="mini" @click.stop="editLocation(row)">编辑</el-button>
              <el-button type="danger" size="mini" @click.stop="removeLocation(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </page-table>
    </div>
    <el-dialog v-dialog-drag title="区域信息" width="800px" :visible.sync="regionVisible" :close-on-press-escape="false"
      :close-on-click-modal="false">
      <el-form ref="regionform" :model="regionData" :rules="regionRules" label-width="110px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="区域编码：" prop="code">
              <el-input v-model="regionData.code" maxlength="20" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域名称：" prop="name">
              <el-input v-model="regionData.name" maxlength="20" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属机构：" prop="dept">
              <tree-box v-model="regionData.dept" :data="deptTree" :expand-all="true" :clearable="false" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在区划：" prop="region">
              <region v-model="regionData.region" root="460000" :start-level="1" with-root any-node
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人：" prop="user">
              <user-chosen v-model="regionData.user" type="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域排序：" prop="ord">
              <el-input-number v-model="regionData.ord" autocomplete="off" :min="1" :max="999" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="区域范围：" prop="scope">
              <el-input v-model="regionData.scope" maxlength="32" autocomplete="off" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="regionVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveRegion">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog v-dialog-drag title="区域地点" width="800px" :visible.sync="locationVisible" :close-on-press-escape="false"
      :close-on-click-modal="false">
      <el-form ref="locationform" :model="locationData" :rules="locationRules" label-width="110px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="区域编码：">
              <el-input v-model="activeItem.code" readonly class="form-static" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域名称：">
              <el-input v-model="activeItem.name" readonly class="form-static" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="网点编码：" prop="code">
              <el-input v-model="locationData.code" maxlength="20" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="网点名称：" prop="name">
              <el-input v-model="locationData.name" maxlength="64" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人：" prop="contact">
              <el-input v-model="locationData.contact" maxlength="20" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话：" prop="phone">
              <el-input v-model="locationData.phone" maxlength="20" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="网点地址：" prop="address">
              <el-input v-model="locationData.address" maxlength="128" autocomplete="off">
                <template slot="append">
                  <el-button size="mini" icon="el-icon-map-location" @click="mapPin">地图定位</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="网点备注：" prop="memo">
              <el-input v-model="locationData.memo" maxlength="128" autocomplete="off" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table ref="optionGrid" :data="amLocationAsset" size="mini" :stripe="true" :border="true">
        <el-table-column type="index" width="50" align="center" />
        <el-table-column label="销售终端编号" prop="sn" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.sn" size="mini" autocomplete="off" />
          </template>
        </el-table-column>
        <el-table-column width="70" align="center">
          <template slot="header">
            <el-button type="success" size="mini" @click="addAsset">新增</el-button>
          </template>
          <template slot-scope="scope">
            <el-button size="mini" type="danger" @click.stop="removeAsset(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="locationVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveLocation">确 定</el-button>
      </div>
    </el-dialog>
    <!--------网点导入------->
    <el-dialog ref="uploadDlg" title="批量导入" fullscreen class="dialog-full" :visible.sync="uploadVisible"
      :close-on-press-escape="false" :close-on-click-modal="false">
      <div style="margin: 10px 20px;">
        <upload-file v-model="fileList" type="ASSET_UPLOAD" simple :multiple="false" :limit="1" accept=".xls,.xlsx"
          @success="uploaded" @removeFile="uploadRemove" />
      </div>
      <div class="upload-block">
        <table>
          <thead>
            <tr>
              <th>行号</th>
              <th>结果提示</th>
              <th>所属市县编号</th>
              <th>销售终端编号</th>
              <th>门店编号</th>
              <th>业主姓名</th>
              <th>负责人</th>
              <th>联系方式</th>
              <th>门店地址</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="item in uploadList">
              <tr v-if="showUploadAll || item.rowMsg != null" :key="item.rowNum" :class="{ err: item.rowMsg != null }">
                <td>{{ item.rowNum }}</td>
                <td class="upload-msg">{{ item.rowMsg }}</td>
                <td>{{ item.region }}</td>
                <td>{{ item.sn }}</td>
                <td>{{ item.code }}</td>
                <td>{{ item.name }}</td>
                <td>{{ item.contact }}</td>
                <td>{{ item.phone }}</td>
                <td>{{ item.address }}</td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
      <div slot="footer" class="dialog-footer">
        <div style="left: 30px; float: left;">
          <el-button size="small" type="warning" @click="toggleErr">{{ showUploadAll ? '查看错误' : '查看全部' }}</el-button>
        </div>
        <el-button size="small" @click="uploadVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="submitUpload">确定上传</el-button>
      </div>
    </el-dialog>
    <el-dialog ref="batchUploadDlg" title="网点批量修改" fullscreen class="dialog-full" :visible.sync="batchUploadVisible"
      :close-on-press-escape="false" :close-on-click-modal="false">
      <div style="margin: 10px 20px;">
        <upload-file v-model="batchFileList" type="ASSET_UPLOAD" simple :multiple="false" :limit="1" accept=".xls,.xlsx"
          @success="batchUploaded" @removeFile="batchUploadRemove" />
      </div>
      <div class="upload-block">
        <table>
          <thead>
            <tr>
              <th>行号</th>
              <th v-if="getBatchErrorCount() > 0">结果提示</th>
              <th>网点编码</th>
              <th>网点名称</th>
              <th>网点备注</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(item, index) in batchUploadList">
              <tr :key="index" :class="{ err: item.rowMsg != null }">
                <td>{{ index + 1 }}</td>
                <td v-if="getBatchErrorCount() > 0" class="upload-msg">{{ item.rowMsg }}</td>
                <td>{{ item.code }}</td>
                <td>{{ item.name }}</td>
                <td>
                  <el-input
                    v-model="item.memo"
                    size="mini"
                    placeholder="请输入网点备注"
                    maxlength="200"
                    show-word-limit
                    clearable
                    style="width: 100%;"
                  />
                </td>
                <td>
                  <el-button type="danger" size="mini" @click="removeBatchRow(index)">删除</el-button>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
      <div slot="footer" class="dialog-footer">
        <div style="left: 30px; float: left;">
          <span style="color: #909399; font-size: 12px;">
            共 {{ batchUploadList.length }} 条数据，其中 {{ getBatchErrorCount() }} 条错误
          </span>
        </div>
        <el-button size="small" @click="batchUploadVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="submitBatchUpdate">确定更新</el-button>
      </div>
    </el-dialog>
    <map-location ref="mapLocation" @success="pined" />
  </div>
</template>
<script>
import { Loading } from 'element-ui'
import PageTable from '@/views/components/PageTable.vue'
import Region from '@/views/components/Region.vue'
import TreeBox from '@/views/components/TreeBox.vue'
import UserChosen from '@/views/components/UserChosen.vue'
import MapLocation from '@/views/map/util/location.vue'
import UploadFile from '@/views/components/UploadFile.vue'

export default {
  components: { PageTable, Region, TreeBox, UserChosen, MapLocation, UploadFile },
  data() {
    return {
      regionList: [],
      deptTree: [], // 机构数据列表
      activeItem: {},
      tableHeight: 300,
      regionVisible: false,
      regionData: { region: '' },
      regionRules: {
        code: [{ required: true, message: '请输入区域编码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],
        dept: [{ required: true, message: '请选择所属机构', trigger: 'blur' }],
        region: [{ required: true, message: '请选择所在区划', trigger: 'blur' }]
      },
      qform: { keyword: '' },
      locationVisible: false,
      locationData: { region: '' },
      locationRules: {
        code: [{ required: true, message: '请输入地点编码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入地点名称', trigger: 'blur' }]
      },
      fileList: [],
      uploadList: [],
      uploadVisible: false,
      showUploadAll: true,
      amLocationAsset: [{}],
      // 批量修改相关
      batchFileList: [],
      batchUploadList: [],
      batchUploadVisible: false
    }
  },
  mounted() {
    this.loadDeptTree()
    this.loadRegion()
    this.searchLocation()
    this.$refs.grid.setMaxHeight(Math.max(document.documentElement.clientHeight - 380, 200))
  },
  methods: {
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    loadDeptTree() {
      this.$http('/sys/dept/treeByType/1').then(res => {
        this.deptTree = res
      }).catch(() => { this.$alert('加载机构树出错') })
    },
    loadRegion() {
      this.$http('/am/region/list').then(res => {
        this.regionList = res || []
        if (this.activeItem && this.activeItem.id) {
          for (let i = 0; i < res.length; i++) {
            if (res[i].id === this.activeItem.id) {
              this.activeItem = res[i]
              break
            }
          }
        }
      })
    },

    showRegion(item) {
      this.activeItem = item
      this.qform.region = item.id
      this.qform.keyword = ''
      this.searchLocation()
    },
    addRegion() {
      this.regionVisible = true
      this.regionData = { ord: 1 }
    },
    editRegion() {
      if (!this.activeItem.id) return
      this.regionVisible = true
      const json = JSON.stringify(this.activeItem)
      this.regionData = JSON.parse(json)
    },
    saveRegion() {
      this.$refs.regionform.validate(valid => {
        if (valid) {
          this.$http({ url: '/am/region/save', data: this.regionData }).then(res => {
            if (res.code > 0) {
              this.$message.success('保存区域信息成功')
              this.regionVisible = false
              this.loadRegion()
            }
          })
        }
      })
    },
    removeRegion() {
      this.$confirm('此操作将永久删除该区域, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        this.$http({ url: '/am/region/delete/' + this.activeItem.id }).then(res => {
          if (res.code > 0) {
            this.$message.success('删除成功')
            this.activeItem = {}
            this.loadRegion()
          }
        })
      }).catch(() => { })
    },
    searchLocation() {
      this.$refs.grid.search(this.qform)
    },
    addLocation() {
      this.locationVisible = true
      this.locationData = { region: this.activeItem.id }
      this.amLocationAsset = []
    },
    // editLocation(item) {
    //   this.locationVisible = true
    //   const json = JSON.stringify(item)
    //   this.locationData = JSON.parse(json)
    // },
    editLocation(item) {
      this.$http('/am/location/get/' + item.id).then(res => {
        if (res.code > 0 && res.data) {
          this.locationVisible = true
          this.locationData = res.data
          this.amLocationAsset = res.data.amLocationAsset || []
        }
      })
    },
    saveLocation() {
      this.$refs.locationform.validate(valid => {
        if (valid) {
          const details = []
          this.amLocationAsset.forEach(r => details.push({ sn: r.sn }))
          if (!details.length) return this.$message.warning('请录入终端信息')
          this.locationData.amLocationAsset = details
          this.$http({ url: '/am/location/saveDevice', data: this.locationData }).then(res => {
            if (res.code > 0) {
              this.$message.success('保存区域信息成功')
              this.locationVisible = false
              this.searchLocation()
            }
          })
        }
      })
    },
    removeLocation(item) {
      this.$confirm('此操作将永久删除该地点, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        this.$http({ url: '/am/location/delete/' + item.id }).then(res => {
          if (res.code > 0) {
            this.$message.success('删除成功')
            this.searchLocation()
          }
        })
      }).catch(() => { })
    },
    mapPin() {
      const ll = this.locationData.lat ? { lng: this.locationData.lng, lat: this.locationData.lat } : null
      this.$refs.mapLocation.show(ll)
    },
    pined(r) {
      this.$set(this.locationData, 'address', r.address)
      this.$set(this.locationData, 'lng', r.lnglat ? r.lnglat.lng : null)
      this.$set(this.locationData, 'lat', r.lnglat ? r.lnglat.lat : null)
    },
    upload() {
      this.fileList = []
      this.uploadList = []
      this.uploadVisible = true
    },
    uploadRemove() {
      this.uploadList = []
    },
    toggleErr() {
      this.showUploadAll = !this.showUploadAll
    },
    uploaded(fileList) {
      if (fileList && fileList.length) {
        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })
        this.$http({ url: '/am/location/uploadFile', data: fileList[0] }).then(res => {
          if (res.code > 0) {
            this.showUploadAll = true
            this.uploadList = res.data
          }
          loadInst.close()
        })
      }
    },
    submitUpload() {
      if (this.uploadList.length === 0) return this.$message.warning('没有可提交的数据')
      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })
      this.$http({ url: '/am/location/uploadData', data: this.uploadList }).then(res => {
        if (res.code === 1) {
          this.$message.success('上传成功')
          this.$emit('success')
          this.uploadVisible = false
          this.search()
        } else if (res.code === 2) {
          this.uploadList = res.data
          this.$message.error('存在错误的数据行')
        }
        loadInst.close()
      }).catch(() => {
        loadInst.close()
        // this.$message.error('网络超时')
      })
    },
    addAsset() {
      this.amLocationAsset.push({})
    },
    removeAsset(rowIndex) {
      this.amLocationAsset.splice(rowIndex, 1)
    },
    // 批量修改相关方法
    handleBatchCommand(command) {
      if (command === 'export') {
        this.exportBatch()
      } else if (command === 'import') {
        this.importBatch()
      }
    },
    exportBatch() {
      if (!this.activeItem || !this.activeItem.id) {
        return this.$message.warning('请先选择一个区域')
      }
      const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })
      this.$jasper({ url: `/am/location/exportBatch/${this.activeItem.id}`, responseType: 'blob' }).then(blob => {
        loadInst.close()
        this.$saveAs(blob, '网点批量修改模板.xlsx')
      }).catch(err => {
        loadInst.close()
        this.$message.error('导出生成出错:' + err)
      })
    },
    importBatch() {
      if (!this.activeItem || !this.activeItem.id) {
        return this.$message.warning('请先选择一个区域')
      }
      this.batchFileList = []
      this.batchUploadList = []
      this.batchUploadVisible = true
    },
    batchUploadRemove() {
      this.batchUploadList = []
    },
    removeBatchRow(index) {
      this.$confirm('确定要删除这条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchUploadList.splice(index, 1)
        this.$message.success('删除成功')
      }).catch(() => {})
    },
    getBatchErrorCount() {
      return this.batchUploadList.filter(item => item.rowMsg != null).length
    },
    batchUploaded(fileList) {
      if (fileList && fileList.length) {
        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })
        this.$http({ url: '/am/location/uploadBatchFile', data: fileList[0] }).then(res => {
          if (res.code > 0) {
            this.batchUploadList = res.data
          }
          loadInst.close()
        })
      }
    },
    submitBatchUpdate() {
      if (this.batchUploadList.length === 0) return this.$message.warning('没有可提交的数据')

      // 过滤出没有错误的数据
      const validData = this.batchUploadList.filter(item => !item.rowMsg)
      if (validData.length === 0) {
        return this.$message.warning('没有有效的数据可以提交，请先删除错误行或修正数据')
      }

      const errorCount = this.getBatchErrorCount()
      if (errorCount > 0) {
        this.$confirm(`当前有 ${errorCount} 条错误数据将被忽略，只提交 ${validData.length} 条有效数据，是否继续？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.checkDuplicatesAndUpdate(validData)
        }).catch(() => {})
      } else {
        this.checkAndConfirmDuplicates(validData)
      }
    },
    checkAndConfirmDuplicates(data) {
      // 检查重复编码
      this.$http({ url: '/am/location/checkBatchDuplicates', data: data }).then(res => {
        if (res.code === 1 && res.data) {
          const duplicateInfo = res.data
          if (duplicateInfo.hasDuplicates) {
            let message = ''
            const importDuplicates = duplicateInfo.importDuplicates || []
            const dbDuplicates = duplicateInfo.dbDuplicates || []

            if (importDuplicates.length > 0) {
              message += `检测到以下网点编码在导入数据中重复出现：${importDuplicates.join(', ')}。\n`
            }

            if (dbDuplicates.length > 0) {
              message += `检测到以下网点编码在数据库中存在多条记录：${dbDuplicates.join(', ')}。\n`
            }
            message += `重复编码将以最后一条数据为准\n`
            message += `是否继续执行批量更新？`

            this.$confirm(message, '发现重复编码', {
              confirmButtonText: '确定更新',
              cancelButtonText: '取消',
              type: 'warning',
              dangerouslyUseHTMLString: false
            }).then(() => {
              this.doBatchUpdate(data)
            }).catch(() => {
              // 用户取消，不执行更新
            })
          } else {
            // 没有重复编码，直接更新
            this.doBatchUpdate(data)
          }
        } else {
          // 检查失败，直接更新
          this.doBatchUpdate(data)
        }
      }).catch(() => {
        // 检查失败，直接更新
        this.doBatchUpdate(data)
      })
    },
    doBatchUpdate(data) {
      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })
      this.$http({ url: '/am/location/batchUpdate', data: data }).then(res => {
        if (res.code === 1) {
          this.$message.success(`批量更新成功`)
          this.batchUploadVisible = false
          this.searchLocation()
        } else if (res.code === 2) {
          this.batchUploadList = res.data
          this.$message.error('部分数据更新失败，请查看错误信息')
        }
        loadInst.close()
      }).catch(() => {
        loadInst.close()
      })
    }
  }
}
</script>
<style lang='scss' scope>
.layout-lr {
  height: calc(100vh - 52px);
}

.layout-lr .left {
  min-width: 220px;
}

.layout-lr .center {
  overflow: hidden;
}

.location-head {
  height: 40px;
}

.location-title {
  line-height: 32px;
  font-size: 16px;
  font-weight: 700;
  color: #303133;
}

.location-search {
  width: 300px;
  float: right;
}

.upload-block {
  padding: 0 4px;
  width: calc(100vw - 12px);
  height: calc(100vh - 250px);
  overflow: auto;
}

.upload-block table {
  border: 1px solid #cccccc;
  border-collapse: collapse;
}

.upload-block table th,
.upload-block table td {
  border: 1px solid #cccccc;
  border-collapse: collapse;
  padding: 4px 6px;
  font-size: 12px;
  line-height: 18px;
}

.upload-block table tr.err {
  background-color: #faee92;
}
</style>
