{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\sys\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\sys\\user\\index.vue", "mtime": 1753321388789}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["PageTable", "TreeBox", "Role", "status", "type", "components", "data", "detailVisible", "opt", "qform", "account", "name", "dept", "deptTree", "form", "gender", "rules", "required", "message", "trigger", "passwordVisible", "passwordform", "created", "loadDeptTree", "$store", "dispatch", "catch", "console", "warn", "mounted", "search", "methods", "isSuperAdminAccount", "userRow", "isSuperAdmin", "undefined", "systemConfig", "getters", "superAdminAccount", "currentUser", "user", "loadSystemConfig", "_this", "$http", "then", "res", "_this2", "$alert", "fnStatus", "r", "c", "v", "i", "fnType", "$refs", "grid", "clearValidate", "_this3", "dataform", "$nextTick", "add", "detail", "Object", "assign", "assignRole", "role", "show", "id", "remove", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "url", "code", "$message", "success", "resetPassword", "savePassword", "_this5", "value", "text", "password", "save", "_this6", "validate", "valid"], "sources": ["src/views/sys/user/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"filter\">\r\n      <el-form :model=\"qform\" inline @submit.native.prevent=\"search\">\r\n        <div class=\"filter-item\">\r\n          <label>帐号：</label>\r\n          <div>\r\n            <el-input v-model=\"qform.account\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <label>姓名：</label>\r\n          <div>\r\n            <el-input v-model=\"qform.name\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <label>所属单位：</label>\r\n          <div>\r\n            <tree-box v-model=\"qform.dept\" :data=\"deptTree\" expand-all clearable />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search\">查询</el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-plus\" @click=\"add\">新增</el-button>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n    <page-table ref=\"grid\" v-table-height path=\"/sys/user/page\" size=\"mini\" stripe border>\r\n      <el-table-column type=\"index\" width=\"50\" />\r\n      <el-table-column label=\"帐号\" prop=\"account\" width=\"120\" />\r\n      <el-table-column label=\"姓名\" prop=\"name\" width=\"150\" />\r\n      <el-table-column label=\"性别\" prop=\"gender\" width=\"50\" align=\"center\" :formatter=\"colGender\" />\r\n      <el-table-column label=\"状态\" prop=\"status\" width=\"60\" align=\"center\" :formatter=\"fnStatus\" />\r\n      <el-table-column label=\"所属机构\" prop=\"deptName\" width=\"100\" />\r\n      <el-table-column label=\"用户类型\" prop=\"type\" width=\"70\" align=\"center\" :formatter=\"fnType\" />\r\n      <el-table-column label=\"角色\" prop=\"roleNames\" />\r\n      <el-table-column label=\"操作\" width=\"250\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"!isSuperAdminAccount(scope.row)\">\r\n            <el-button type=\"primary\" size=\"mini\" @click.stop=\"detail(scope.row)\">编辑</el-button>\r\n            <el-button type=\"success\" size=\"mini\" @click.stop=\"assignRole(scope.row)\">角色</el-button>\r\n            <el-button type=\"danger\" size=\"mini\" @click.stop=\"remove(scope.row)\">删除</el-button>\r\n            <el-button type=\"warning\" size=\"mini\" @click.stop=\"resetPassword(scope.row)\">重置密码</el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </page-table>\r\n    <el-dialog v-dialog-drag title=\"用户信息\" :visible.sync=\"detailVisible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"dataform\" :model=\"form\" :rules=\"rules\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"所属机构：\" prop=\"dept\">\r\n              <el-input v-if=\"/2|3/.test(form.type)\" v-model=\"form.deptName\" readonly class=\"readonly\" />\r\n              <tree-box v-else v-model=\"form.dept\" :data=\"deptTree\" expand-all :clearable=\"false\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"姓名：\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性别：\" prop=\"gender\">\r\n              <el-radio-group v-model=\"form.gender\">\r\n                <el-radio label=\"1\">男</el-radio>\r\n                <el-radio label=\"2\">女</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"帐号：\" prop=\"account\">\r\n              <el-input v-model=\"form.account\" :readonly=\"opt == 'update'\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <span class=\"form-memo\">{{ opt == 'add' ? '初始化密码为：Hntc@1234' : '帐号不能修改' }}</span>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"编码：\" prop=\"no\">\r\n              <el-input v-model=\"form.no\" maxlength=\"12\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n              <el-input v-model=\"form.phone\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <role ref=\"role\" @success=\"search\" />\r\n    <el-dialog v-dialog-drag title=\"重置密码\" :visible.sync=\"passwordVisible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"passwordform\" :model=\"passwordform\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"姓名：\" prop=\"name\">\r\n              <el-input v-model=\"passwordform.name\" readonly class=\"readonly\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"帐号：\" prop=\"account\">\r\n              <el-input v-model=\"passwordform.account\" readonly class=\"readonly\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"重置密码：\" prop=\"password\">\r\n              <el-input v-model=\"passwordform.password\" maxlength=\"64\" autocomplete=\"off\" placeholder=\"不填表示重置为Hntc@1234\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <span class=\"form-memo\">密码包含：大写字母、小写字母、数字和特殊符号,长度8~20</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"passwordVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"savePassword\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport Role from './role.vue'\r\nconst status = { '1': '在职', '5': '离职', '8': '禁用' }\r\nconst type = { '1': '内部用户', '2': '外部用户' }\r\n\r\nexport default {\r\n  components: { PageTable, TreeBox, Role },\r\n  data() {\r\n    return {\r\n      detailVisible: false,\r\n      opt: null, // 操作类型\r\n      qform: { account: null, name: null, dept: null },\r\n      // 机构数据列表\r\n      deptTree: [],\r\n      form: { gender: '1' },\r\n      rules: {\r\n        dept: [{ required: true, message: '请选择部门' }],\r\n        account: [{ required: true, message: '请输入帐号', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\r\n        type: [{ required: true, message: '请输入用户类型', trigger: 'blur' }]\r\n      },\r\n      passwordVisible: false,\r\n      passwordform: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.loadDeptTree()\r\n    // 加载系统配置\r\n    this.$store.dispatch('user/loadSystemConfig').catch(() => {\r\n      console.warn('获取系统配置失败')\r\n    })\r\n  },\r\n  mounted() {\r\n    this.search()\r\n  },\r\n  methods: {\r\n    isSuperAdminAccount(userRow) {\r\n      // 判断是否为超级管理员账号\r\n      // 方案1：如果后端返回的用户列表中包含isSuperAdmin字段，直接使用\r\n      if (userRow.isSuperAdmin !== undefined) {\r\n        return userRow.isSuperAdmin\r\n      }\r\n\r\n      // 方案2：通过从后端获取的超级管理员账号名进行比较\r\n      const systemConfig = this.$store.getters.systemConfig\r\n      if (systemConfig && systemConfig.superAdminAccount) {\r\n        return systemConfig.superAdminAccount === userRow.account\r\n      }\r\n\r\n      // 方案3：通过当前登录用户的账号进行比较（备用方案）\r\n      const currentUser = this.$store.getters.user\r\n      if (currentUser && currentUser.isSuperAdmin) {\r\n        return currentUser.account === userRow.account\r\n      }\r\n\r\n      return false\r\n    },\r\n    loadSystemConfig() {\r\n      // 从后端获取系统配置，包括超级管理员账号名\r\n      this.$http('/app/config').then(res => {\r\n        if (res && res.superAdminAccount) {\r\n          this.superAdminAccount = res.superAdminAccount\r\n        }\r\n      }).catch(() => {\r\n        console.warn('获取系统配置失败')\r\n      })\r\n    },\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    fnStatus(r, c, v, i) {\r\n      return status[v] || ''\r\n    },\r\n    fnType(r, c, v, i) {\r\n      return type[v] || ''\r\n    },\r\n    search() {\r\n      if (this.qform.dept === '0') this.qform.dept = null\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    clearValidate() {\r\n      if (this.$refs.dataform) {\r\n        this.$refs.dataform.clearValidate()\r\n      } else {\r\n        this.$nextTick(() => {\r\n          this.$refs.dataform.clearValidate()\r\n        })\r\n      }\r\n    },\r\n    add() {\r\n      this.opt = 'add'\r\n      this.detailVisible = true\r\n      this.clearValidate()\r\n      this.form = { gender: '1' }\r\n    },\r\n    detail(data) {\r\n      this.opt = 'update'\r\n      this.detailVisible = true\r\n      this.clearValidate()\r\n      this.form = Object.assign({}, data)\r\n    },\r\n    assignRole(data) {\r\n      this.$refs.role.show(data.id)\r\n    },\r\n    remove(data) {\r\n      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })\r\n        .then(() => {\r\n          this.$http({ url: '/sys/user/delete/' + data.id }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('删除成功')\r\n              this.detailVisible = false\r\n              this.search()\r\n            }\r\n          })\r\n        }).catch(() => {})\r\n    },\r\n    resetPassword(data) {\r\n      this.passwordform = data\r\n      this.passwordVisible = true\r\n    },\r\n    savePassword() {\r\n      this.$http({ url: '/sys/user/resetPassword', data: { value: this.passwordform.id, text: this.passwordform.password }}).then(res => {\r\n        if (res.code > 0) {\r\n          this.$message.success('重置成功')\r\n          this.passwordVisible = false\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    save() {\r\n      this.$refs.dataform.validate(valid => {\r\n        if (valid) {\r\n          if (!this.form.type) this.form.type = '1'\r\n          this.$http({ url: '/sys/user/' + this.opt, data: this.form }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('提交成功')\r\n              this.detailVisible = false\r\n              this.search()\r\n            }\r\n          }).catch(() => {})\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoIA,OAAAA,SAAA;AACA,OAAAC,OAAA;AACA,OAAAC,IAAA;AACA,IAAAC,MAAA;EAAA;EAAA;EAAA;AAAA;AACA,IAAAC,IAAA;EAAA;EAAA;AAAA;AAEA;EACAC,UAAA;IAAAL,SAAA,EAAAA,SAAA;IAAAC,OAAA,EAAAA,OAAA;IAAAC,IAAA,EAAAA;EAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,GAAA;MAAA;MACAC,KAAA;QAAAC,OAAA;QAAAC,IAAA;QAAAC,IAAA;MAAA;MACA;MACAC,QAAA;MACAC,IAAA;QAAAC,MAAA;MAAA;MACAC,KAAA;QACAJ,IAAA;UAAAK,QAAA;UAAAC,OAAA;QAAA;QACAR,OAAA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAR,IAAA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAf,IAAA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,eAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA;IACA,KAAAC,MAAA,CAAAC,QAAA,0BAAAC,KAAA;MACAC,OAAA,CAAAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,MAAA;EACA;EACAC,OAAA;IACAC,mBAAA,WAAAA,oBAAAC,OAAA;MACA;MACA;MACA,IAAAA,OAAA,CAAAC,YAAA,KAAAC,SAAA;QACA,OAAAF,OAAA,CAAAC,YAAA;MACA;;MAEA;MACA,IAAAE,YAAA,QAAAZ,MAAA,CAAAa,OAAA,CAAAD,YAAA;MACA,IAAAA,YAAA,IAAAA,YAAA,CAAAE,iBAAA;QACA,OAAAF,YAAA,CAAAE,iBAAA,KAAAL,OAAA,CAAAvB,OAAA;MACA;;MAEA;MACA,IAAA6B,WAAA,QAAAf,MAAA,CAAAa,OAAA,CAAAG,IAAA;MACA,IAAAD,WAAA,IAAAA,WAAA,CAAAL,YAAA;QACA,OAAAK,WAAA,CAAA7B,OAAA,KAAAuB,OAAA,CAAAvB,OAAA;MACA;MAEA;IACA;IACA+B,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,KAAA;MACA;MACA,KAAAC,KAAA,gBAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA,CAAAP,iBAAA;UACAI,KAAA,CAAAJ,iBAAA,GAAAO,GAAA,CAAAP,iBAAA;QACA;MACA,GAAAZ,KAAA;QACAC,OAAA,CAAAC,IAAA;MACA;IACA;IACAL,YAAA,WAAAA,aAAA;MAAA,IAAAuB,MAAA;MACA,KAAAH,KAAA,2BAAAC,IAAA,WAAAC,GAAA;QACAC,MAAA,CAAAjC,QAAA,GAAAgC,GAAA;MACA,GAAAnB,KAAA;QAAAoB,MAAA,CAAAC,MAAA;MAAA;IACA;IACAC,QAAA,WAAAA,SAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA;MACA,OAAAjD,MAAA,CAAAgD,CAAA;IACA;IACAE,MAAA,WAAAA,OAAAJ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA;MACA,OAAAhD,IAAA,CAAA+C,CAAA;IACA;IACArB,MAAA,WAAAA,OAAA;MACA,SAAArB,KAAA,CAAAG,IAAA,eAAAH,KAAA,CAAAG,IAAA;MACA,KAAA0C,KAAA,CAAAC,IAAA,CAAAzB,MAAA,MAAArB,KAAA;IACA;IACA+C,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAAH,KAAA,CAAAI,QAAA;QACA,KAAAJ,KAAA,CAAAI,QAAA,CAAAF,aAAA;MACA;QACA,KAAAG,SAAA;UACAF,MAAA,CAAAH,KAAA,CAAAI,QAAA,CAAAF,aAAA;QACA;MACA;IACA;IACAI,GAAA,WAAAA,IAAA;MACA,KAAApD,GAAA;MACA,KAAAD,aAAA;MACA,KAAAiD,aAAA;MACA,KAAA1C,IAAA;QAAAC,MAAA;MAAA;IACA;IACA8C,MAAA,WAAAA,OAAAvD,IAAA;MACA,KAAAE,GAAA;MACA,KAAAD,aAAA;MACA,KAAAiD,aAAA;MACA,KAAA1C,IAAA,GAAAgD,MAAA,CAAAC,MAAA,KAAAzD,IAAA;IACA;IACA0D,UAAA,WAAAA,WAAA1D,IAAA;MACA,KAAAgD,KAAA,CAAAW,IAAA,CAAAC,IAAA,CAAA5D,IAAA,CAAA6D,EAAA;IACA;IACAC,MAAA,WAAAA,OAAA9D,IAAA;MAAA,IAAA+D,MAAA;MACA,KAAAC,QAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAApE,IAAA;MAAA,GACAwC,IAAA;QACAyB,MAAA,CAAA1B,KAAA;UAAA8B,GAAA,wBAAAnE,IAAA,CAAA6D;QAAA,GAAAvB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA6B,IAAA;YACAL,MAAA,CAAAM,QAAA,CAAAC,OAAA;YACAP,MAAA,CAAA9D,aAAA;YACA8D,MAAA,CAAAvC,MAAA;UACA;QACA;MACA,GAAAJ,KAAA;IACA;IACAmD,aAAA,WAAAA,cAAAvE,IAAA;MACA,KAAAe,YAAA,GAAAf,IAAA;MACA,KAAAc,eAAA;IACA;IACA0D,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAApC,KAAA;QAAA8B,GAAA;QAAAnE,IAAA;UAAA0E,KAAA,OAAA3D,YAAA,CAAA8C,EAAA;UAAAc,IAAA,OAAA5D,YAAA,CAAA6D;QAAA;MAAA,GAAAtC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA6B,IAAA;UACAK,MAAA,CAAAJ,QAAA,CAAAC,OAAA;UACAG,MAAA,CAAA3D,eAAA;QACA;MACA,GAAAM,KAAA;IACA;IACAyD,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MACA,KAAA9B,KAAA,CAAAI,QAAA,CAAA2B,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAF,MAAA,CAAAtE,IAAA,CAAAV,IAAA,EAAAgF,MAAA,CAAAtE,IAAA,CAAAV,IAAA;UACAgF,MAAA,CAAAzC,KAAA;YAAA8B,GAAA,iBAAAW,MAAA,CAAA5E,GAAA;YAAAF,IAAA,EAAA8E,MAAA,CAAAtE;UAAA,GAAA8B,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAA6B,IAAA;cACAU,MAAA,CAAAT,QAAA,CAAAC,OAAA;cACAQ,MAAA,CAAA7E,aAAA;cACA6E,MAAA,CAAAtD,MAAA;YACA;UACA,GAAAJ,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}