{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\sys\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\sys\\user\\index.vue", "mtime": 1753321654844}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["PageTable", "TreeBox", "Role", "status", "type", "components", "data", "detailVisible", "opt", "qform", "account", "name", "dept", "deptTree", "form", "gender", "rules", "required", "message", "trigger", "passwordVisible", "passwordform", "created", "loadDeptTree", "$store", "dispatch", "catch", "console", "warn", "mounted", "search", "methods", "isSuperAdminAccount", "userRow", "systemConfig", "getters", "superAdminAccount", "_this", "$http", "then", "res", "$alert", "fnStatus", "r", "c", "v", "i", "fnType", "$refs", "grid", "clearValidate", "_this2", "dataform", "$nextTick", "add", "detail", "Object", "assign", "assignRole", "role", "show", "id", "remove", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "url", "code", "$message", "success", "resetPassword", "savePassword", "_this4", "value", "text", "password", "save", "_this5", "validate", "valid"], "sources": ["src/views/sys/user/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"filter\">\r\n      <el-form :model=\"qform\" inline @submit.native.prevent=\"search\">\r\n        <div class=\"filter-item\">\r\n          <label>帐号：</label>\r\n          <div>\r\n            <el-input v-model=\"qform.account\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <label>姓名：</label>\r\n          <div>\r\n            <el-input v-model=\"qform.name\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <label>所属单位：</label>\r\n          <div>\r\n            <tree-box v-model=\"qform.dept\" :data=\"deptTree\" expand-all clearable />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search\">查询</el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-plus\" @click=\"add\">新增</el-button>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n    <page-table ref=\"grid\" v-table-height path=\"/sys/user/page\" size=\"mini\" stripe border>\r\n      <el-table-column type=\"index\" width=\"50\" />\r\n      <el-table-column label=\"帐号\" prop=\"account\" width=\"120\" />\r\n      <el-table-column label=\"姓名\" prop=\"name\" width=\"150\" />\r\n      <el-table-column label=\"性别\" prop=\"gender\" width=\"50\" align=\"center\" :formatter=\"colGender\" />\r\n      <el-table-column label=\"状态\" prop=\"status\" width=\"60\" align=\"center\" :formatter=\"fnStatus\" />\r\n      <el-table-column label=\"所属机构\" prop=\"deptName\" width=\"100\" />\r\n      <el-table-column label=\"用户类型\" prop=\"type\" width=\"70\" align=\"center\" :formatter=\"fnType\" />\r\n      <el-table-column label=\"角色\" prop=\"roleNames\" />\r\n      <el-table-column label=\"操作\" width=\"250\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"!isSuperAdminAccount(scope.row)\">\r\n            <el-button type=\"primary\" size=\"mini\" @click.stop=\"detail(scope.row)\">编辑</el-button>\r\n            <el-button type=\"success\" size=\"mini\" @click.stop=\"assignRole(scope.row)\">角色</el-button>\r\n            <el-button type=\"danger\" size=\"mini\" @click.stop=\"remove(scope.row)\">删除</el-button>\r\n            <el-button type=\"warning\" size=\"mini\" @click.stop=\"resetPassword(scope.row)\">重置密码</el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </page-table>\r\n    <el-dialog v-dialog-drag title=\"用户信息\" :visible.sync=\"detailVisible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"dataform\" :model=\"form\" :rules=\"rules\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"所属机构：\" prop=\"dept\">\r\n              <el-input v-if=\"/2|3/.test(form.type)\" v-model=\"form.deptName\" readonly class=\"readonly\" />\r\n              <tree-box v-else v-model=\"form.dept\" :data=\"deptTree\" expand-all :clearable=\"false\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"姓名：\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性别：\" prop=\"gender\">\r\n              <el-radio-group v-model=\"form.gender\">\r\n                <el-radio label=\"1\">男</el-radio>\r\n                <el-radio label=\"2\">女</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"帐号：\" prop=\"account\">\r\n              <el-input v-model=\"form.account\" :readonly=\"opt == 'update'\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <span class=\"form-memo\">{{ opt == 'add' ? '初始化密码为：Hntc@1234' : '帐号不能修改' }}</span>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"编码：\" prop=\"no\">\r\n              <el-input v-model=\"form.no\" maxlength=\"12\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n              <el-input v-model=\"form.phone\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <role ref=\"role\" @success=\"search\" />\r\n    <el-dialog v-dialog-drag title=\"重置密码\" :visible.sync=\"passwordVisible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"passwordform\" :model=\"passwordform\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"姓名：\" prop=\"name\">\r\n              <el-input v-model=\"passwordform.name\" readonly class=\"readonly\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"帐号：\" prop=\"account\">\r\n              <el-input v-model=\"passwordform.account\" readonly class=\"readonly\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"重置密码：\" prop=\"password\">\r\n              <el-input v-model=\"passwordform.password\" maxlength=\"64\" autocomplete=\"off\" placeholder=\"不填表示重置为Hntc@1234\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <span class=\"form-memo\">密码包含：大写字母、小写字母、数字和特殊符号,长度8~20</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"passwordVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"savePassword\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport Role from './role.vue'\r\nconst status = { '1': '在职', '5': '离职', '8': '禁用' }\r\nconst type = { '1': '内部用户', '2': '外部用户' }\r\n\r\nexport default {\r\n  components: { PageTable, TreeBox, Role },\r\n  data() {\r\n    return {\r\n      detailVisible: false,\r\n      opt: null, // 操作类型\r\n      qform: { account: null, name: null, dept: null },\r\n      // 机构数据列表\r\n      deptTree: [],\r\n      form: { gender: '1' },\r\n      rules: {\r\n        dept: [{ required: true, message: '请选择部门' }],\r\n        account: [{ required: true, message: '请输入帐号', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\r\n        type: [{ required: true, message: '请输入用户类型', trigger: 'blur' }]\r\n      },\r\n      passwordVisible: false,\r\n      passwordform: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.loadDeptTree()\r\n    // 加载系统配置\r\n    this.$store.dispatch('user/loadSystemConfig').catch(() => {\r\n      console.warn('获取系统配置失败')\r\n    })\r\n  },\r\n  mounted() {\r\n    this.search()\r\n  },\r\n  methods: {\r\n    isSuperAdminAccount(userRow) {\r\n      // 判断是否为超级管理员账号\r\n      const systemConfig = this.$store.getters.systemConfig\r\n      if (systemConfig && systemConfig.superAdminAccount) {\r\n        return systemConfig.superAdminAccount === userRow.account\r\n      }\r\n      return false\r\n    },\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    fnStatus(r, c, v, i) {\r\n      return status[v] || ''\r\n    },\r\n    fnType(r, c, v, i) {\r\n      return type[v] || ''\r\n    },\r\n    search() {\r\n      if (this.qform.dept === '0') this.qform.dept = null\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    clearValidate() {\r\n      if (this.$refs.dataform) {\r\n        this.$refs.dataform.clearValidate()\r\n      } else {\r\n        this.$nextTick(() => {\r\n          this.$refs.dataform.clearValidate()\r\n        })\r\n      }\r\n    },\r\n    add() {\r\n      this.opt = 'add'\r\n      this.detailVisible = true\r\n      this.clearValidate()\r\n      this.form = { gender: '1' }\r\n    },\r\n    detail(data) {\r\n      this.opt = 'update'\r\n      this.detailVisible = true\r\n      this.clearValidate()\r\n      this.form = Object.assign({}, data)\r\n    },\r\n    assignRole(data) {\r\n      this.$refs.role.show(data.id)\r\n    },\r\n    remove(data) {\r\n      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })\r\n        .then(() => {\r\n          this.$http({ url: '/sys/user/delete/' + data.id }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('删除成功')\r\n              this.detailVisible = false\r\n              this.search()\r\n            }\r\n          })\r\n        }).catch(() => {})\r\n    },\r\n    resetPassword(data) {\r\n      this.passwordform = data\r\n      this.passwordVisible = true\r\n    },\r\n    savePassword() {\r\n      this.$http({ url: '/sys/user/resetPassword', data: { value: this.passwordform.id, text: this.passwordform.password }}).then(res => {\r\n        if (res.code > 0) {\r\n          this.$message.success('重置成功')\r\n          this.passwordVisible = false\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    save() {\r\n      this.$refs.dataform.validate(valid => {\r\n        if (valid) {\r\n          if (!this.form.type) this.form.type = '1'\r\n          this.$http({ url: '/sys/user/' + this.opt, data: this.form }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('提交成功')\r\n              this.detailVisible = false\r\n              this.search()\r\n            }\r\n          }).catch(() => {})\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoIA,OAAAA,SAAA;AACA,OAAAC,OAAA;AACA,OAAAC,IAAA;AACA,IAAAC,MAAA;EAAA;EAAA;EAAA;AAAA;AACA,IAAAC,IAAA;EAAA;EAAA;AAAA;AAEA;EACAC,UAAA;IAAAL,SAAA,EAAAA,SAAA;IAAAC,OAAA,EAAAA,OAAA;IAAAC,IAAA,EAAAA;EAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,GAAA;MAAA;MACAC,KAAA;QAAAC,OAAA;QAAAC,IAAA;QAAAC,IAAA;MAAA;MACA;MACAC,QAAA;MACAC,IAAA;QAAAC,MAAA;MAAA;MACAC,KAAA;QACAJ,IAAA;UAAAK,QAAA;UAAAC,OAAA;QAAA;QACAR,OAAA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAR,IAAA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAf,IAAA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,eAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA;IACA,KAAAC,MAAA,CAAAC,QAAA,0BAAAC,KAAA;MACAC,OAAA,CAAAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,MAAA;EACA;EACAC,OAAA;IACAC,mBAAA,WAAAA,oBAAAC,OAAA;MACA;MACA,IAAAC,YAAA,QAAAV,MAAA,CAAAW,OAAA,CAAAD,YAAA;MACA,IAAAA,YAAA,IAAAA,YAAA,CAAAE,iBAAA;QACA,OAAAF,YAAA,CAAAE,iBAAA,KAAAH,OAAA,CAAAvB,OAAA;MACA;MACA;IACA;IACAa,YAAA,WAAAA,aAAA;MAAA,IAAAc,KAAA;MACA,KAAAC,KAAA,2BAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAxB,QAAA,GAAA2B,GAAA;MACA,GAAAd,KAAA;QAAAW,KAAA,CAAAI,MAAA;MAAA;IACA;IACAC,QAAA,WAAAA,SAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA;MACA,OAAA3C,MAAA,CAAA0C,CAAA;IACA;IACAE,MAAA,WAAAA,OAAAJ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA;MACA,OAAA1C,IAAA,CAAAyC,CAAA;IACA;IACAf,MAAA,WAAAA,OAAA;MACA,SAAArB,KAAA,CAAAG,IAAA,eAAAH,KAAA,CAAAG,IAAA;MACA,KAAAoC,KAAA,CAAAC,IAAA,CAAAnB,MAAA,MAAArB,KAAA;IACA;IACAyC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAAH,KAAA,CAAAI,QAAA;QACA,KAAAJ,KAAA,CAAAI,QAAA,CAAAF,aAAA;MACA;QACA,KAAAG,SAAA;UACAF,MAAA,CAAAH,KAAA,CAAAI,QAAA,CAAAF,aAAA;QACA;MACA;IACA;IACAI,GAAA,WAAAA,IAAA;MACA,KAAA9C,GAAA;MACA,KAAAD,aAAA;MACA,KAAA2C,aAAA;MACA,KAAApC,IAAA;QAAAC,MAAA;MAAA;IACA;IACAwC,MAAA,WAAAA,OAAAjD,IAAA;MACA,KAAAE,GAAA;MACA,KAAAD,aAAA;MACA,KAAA2C,aAAA;MACA,KAAApC,IAAA,GAAA0C,MAAA,CAAAC,MAAA,KAAAnD,IAAA;IACA;IACAoD,UAAA,WAAAA,WAAApD,IAAA;MACA,KAAA0C,KAAA,CAAAW,IAAA,CAAAC,IAAA,CAAAtD,IAAA,CAAAuD,EAAA;IACA;IACAC,MAAA,WAAAA,OAAAxD,IAAA;MAAA,IAAAyD,MAAA;MACA,KAAAC,QAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAA9D,IAAA;MAAA,GACAmC,IAAA;QACAwB,MAAA,CAAAzB,KAAA;UAAA6B,GAAA,wBAAA7D,IAAA,CAAAuD;QAAA,GAAAtB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA4B,IAAA;YACAL,MAAA,CAAAM,QAAA,CAAAC,OAAA;YACAP,MAAA,CAAAxD,aAAA;YACAwD,MAAA,CAAAjC,MAAA;UACA;QACA;MACA,GAAAJ,KAAA;IACA;IACA6C,aAAA,WAAAA,cAAAjE,IAAA;MACA,KAAAe,YAAA,GAAAf,IAAA;MACA,KAAAc,eAAA;IACA;IACAoD,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAnC,KAAA;QAAA6B,GAAA;QAAA7D,IAAA;UAAAoE,KAAA,OAAArD,YAAA,CAAAwC,EAAA;UAAAc,IAAA,OAAAtD,YAAA,CAAAuD;QAAA;MAAA,GAAArC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA4B,IAAA;UACAK,MAAA,CAAAJ,QAAA,CAAAC,OAAA;UACAG,MAAA,CAAArD,eAAA;QACA;MACA,GAAAM,KAAA;IACA;IACAmD,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MACA,KAAA9B,KAAA,CAAAI,QAAA,CAAA2B,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAF,MAAA,CAAAhE,IAAA,CAAAV,IAAA,EAAA0E,MAAA,CAAAhE,IAAA,CAAAV,IAAA;UACA0E,MAAA,CAAAxC,KAAA;YAAA6B,GAAA,iBAAAW,MAAA,CAAAtE,GAAA;YAAAF,IAAA,EAAAwE,MAAA,CAAAhE;UAAA,GAAAyB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAA4B,IAAA;cACAU,MAAA,CAAAT,QAAA,CAAAC,OAAA;cACAQ,MAAA,CAAAvE,aAAA;cACAuE,MAAA,CAAAhD,MAAA;YACA;UACA,GAAAJ,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}