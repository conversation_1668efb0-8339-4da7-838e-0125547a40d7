{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\rp\\asset\\biz.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\rp\\asset\\biz.vue", "mtime": 1753319707009}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUGFnZVRhYmxlIGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9QYWdlVGFibGUudnVlJw0KaW1wb3J0IENlbnRlckRlcHRUcmVlQm94IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9DZW50ZXJEZXB0VHJlZUJveC52dWUnDQppbXBvcnQgQml6QmlsbENyZWF0ZSBmcm9tICcuL0JpekJpbGxDcmVhdGUudnVlJw0KaW1wb3J0IEJpekJpbGxFZGl0IGZyb20gJy4vQml6QmlsbEVkaXQudnVlJw0KaW1wb3J0IEJpekJpbGxDaGVjayBmcm9tICcuL0JpekJpbGxDaGVjay52dWUnDQppbXBvcnQgQml6QmlsbFZpZXcgZnJvbSAnLi9CaXpCaWxsVmlldy52dWUnDQppbXBvcnQgeyBMb2FkaW5nIH0gZnJvbSAnZWxlbWVudC11aScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IFBhZ2VUYWJsZSwgQ2VudGVyRGVwdFRyZWVCb3gsIEJpekJpbGxDcmVhdGUsIEJpekJpbGxFZGl0LCBCaXpCaWxsQ2hlY2ssIEJpekJpbGxWaWV3IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGZvcm1MYWJlbFdpZHRoOiAnMTAwcHgnLA0KICAgICAgZnVsbHNjcmVlbkxvYWRpbmc6IGZhbHNlLA0KICAgICAgZGVwdFZpc2libGU6IGZhbHNlLA0KICAgICAgcWZvcm06IHsNCiAgICAgICAgZGVwdDogbnVsbCwNCiAgICAgICAgbW9udGg6IG51bGwNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgY29uc3QgdXNlciA9IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlcg0KICAgIHRoaXMuZGVwdFZpc2libGUgPSB1c2VyLmlzU3VwZXJBZG1pbiB8fCB1c2VyLnBhcmVudERlcHQgPT09ICcwJw0KICAgIHRoaXMuc2VhcmNoKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGNvbE1vbnRoKHIsIGMsIHYpIHsNCiAgICAgIHJldHVybiB2LnN1YnN0cmluZygwLCA0KSArICflubQnICsgdi5zdWJzdHJpbmcoNCkgKyAn5pyIJw0KICAgIH0sDQogICAgc2VhcmNoKCkgew0KICAgICAgdGhpcy4kcmVmcy5ncmlkLnNlYXJjaCh0aGlzLnFmb3JtKQ0KICAgIH0sDQogICAgYWRkQmlsbCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuYmlsbENyZWF0ZS5zaG93KHt9KQ0KICAgIH0sDQogICAgZWRpdEJpbGwoZGF0YSkgew0KICAgICAgdGhpcy5mdWxsc2NyZWVuTG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMuJGh0dHAoJy9ycC96ZGovemJ0L2dldC8nICsgZGF0YS5pZCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmZ1bGxzY3JlZW5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuYmlsbEVkaXQuc2hvdyhyZXMuZGF0YSkNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmZ1bGxzY3JlZW5Mb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBjaGVja0JpbGwoZGF0YSkgew0KICAgICAgdGhpcy5mdWxsc2NyZWVuTG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMuJGh0dHAoJy9ycC96ZGovemJ0L2dldC8nICsgZGF0YS5pZCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmZ1bGxzY3JlZW5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuYmlsbENoZWNrLnNob3cocmVzLmRhdGEpDQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5mdWxsc2NyZWVuTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgdmlld0JpbGwoZGF0YSkgew0KICAgICAgdGhpcy5mdWxsc2NyZWVuTG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMuJGh0dHAoJy9ycC96ZGovemJ0L2dldC8nICsgZGF0YS5pZCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmZ1bGxzY3JlZW5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuYmlsbFZpZXcuc2hvdyhyZXMuZGF0YSkNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmZ1bGxzY3JlZW5Mb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBleHBvcnRCaWxsKGRhdGEpIHsNCiAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+ato+WcqOWvvOWHuuaWh+S7tu+8jOivt+iAkOW/g+etieW+hS4uLicgfSkNCiAgICAgIHRoaXMuJGphc3Blcih7IHVybDogJy9ycC96ZGovemJ0L2V4cG9ydC8nICsgZGF0YS5pZCwgcmVzcG9uc2VUeXBlOiAnYmxvYicgfSkudGhlbihibG9iID0+IHsNCiAgICAgICAgbG9hZEluc3QuY2xvc2UoKQ0KICAgICAgICB0aGlzLiRzYXZlQXMoYmxvYiwgZGF0YS5kZXB0TmFtZSArICdfJyArIGRhdGEubW9udGggKyAnLnhsc3gnKQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgbG9hZEluc3QuY2xvc2UoKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflr7zlh7rmiqXooajnlJ/miJDlh7rplJk6JyArIGVycikNCiAgICAgIH0pDQogICAgfSwNCiAgICByZW1vdmVCaWxsKGRhdGEpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaKpeihqCwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7IGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsIHR5cGU6ICd3YXJuaW5nJyB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kaHR0cCgnL3JwL3pkai96YnQvZGVsZXRlLycgKyBkYXRhLmlkKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgdGhpcy5kZXRhaWxWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICAgIHRoaXMuc2VhcmNoKCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["biz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "biz.vue", "sourceRoot": "src/views/rp/asset", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"filter\">\r\n      <el-form :model=\"qform\" label-width=\"100px\" @submit.native.prevent=\"search\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"10\">\r\n            <el-form-item v-if=\"deptVisible\" label=\"中心：\">\r\n              <center-dept-tree-box v-model=\"qform.dept\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"月份：\">\r\n              <el-date-picker v-model=\"qform.month\" type=\"month\" value-format=\"yyyyMM\" placeholder=\"选择月\" style=\"width:130px;\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-button class=\"filter-item\" type=\"primary\" icon=\"el-icon-search\" @click=\"search\">查询</el-button>\r\n            <el-button v-loading.fullscreen.lock=\"fullscreenLoading\" class=\"filter-item\" type=\"success\" icon=\"el-icon-plus\" @click=\"addBill\">新增</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n    </div>\r\n    <page-table ref=\"grid\" v-table-height path=\"/rp/zdj/zbt/page\" :query=\"qform\" stripe border size=\"mini\">\r\n      <el-table-column type=\"index\" width=\"50\" />\r\n      <el-table-column label=\"部门\" prop=\"deptName\" width=\"100\" />\r\n      <el-table-column label=\"月份\" prop=\"month\" width=\"90\" align=\"center\" :formatter=\"colMonth\" />\r\n      <el-table-column label=\"生成时间\" prop=\"buildTime\" width=\"140\" align=\"center\" />\r\n      <el-table-column label=\"操作人\" prop=\"buildUserName\" width=\"80\" align=\"center\" />\r\n      <el-table-column label=\"核定时间\" prop=\"checkTime\" width=\"140\" align=\"center\" />\r\n      <el-table-column label=\"核定人\" prop=\"checkUserName\" width=\"80\" align=\"center\" />\r\n      <el-table-column label=\"备注\" prop=\"memo\" />\r\n      <el-table-column label=\"操作\" width=\"220\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button v-loading.fullscreen.lock=\"fullscreenLoading\" size=\"mini\" type=\"primary\" @click.stop=\"viewBill(scope.row)\">查看</el-button>\r\n          <template v-if=\"scope.row.status === '1'\">\r\n            <el-button v-loading.fullscreen.lock=\"fullscreenLoading\" size=\"mini\" type=\"primary\" @click.stop=\"editBill(scope.row)\">编辑</el-button>\r\n            <el-button v-loading.fullscreen.lock=\"fullscreenLoading\" size=\"mini\" type=\"success\" @click.stop=\"checkBill(scope.row)\">核定</el-button>\r\n          </template>\r\n          <template v-else>\r\n            <el-button v-loading.fullscreen.lock=\"fullscreenLoading\" size=\"mini\" type=\"success\" @click.stop=\"exportBill(scope.row)\">导出</el-button>\r\n          </template>\r\n          <el-button size=\"mini\" type=\"danger\" @click.stop=\"removeBill(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </page-table>\r\n    <biz-bill-create ref=\"billCreate\" @success=\"search\" />\r\n    <biz-bill-edit ref=\"billEdit\" @success=\"search\" />\r\n    <biz-bill-check ref=\"billCheck\" @success=\"search\" />\r\n    <biz-bill-view ref=\"billView\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport CenterDeptTreeBox from '@/views/components/CenterDeptTreeBox.vue'\r\nimport BizBillCreate from './BizBillCreate.vue'\r\nimport BizBillEdit from './BizBillEdit.vue'\r\nimport BizBillCheck from './BizBillCheck.vue'\r\nimport BizBillView from './BizBillView.vue'\r\nimport { Loading } from 'element-ui'\r\n\r\nexport default {\r\n  components: { PageTable, CenterDeptTreeBox, BizBillCreate, BizBillEdit, BizBillCheck, BizBillView },\r\n  data() {\r\n    return {\r\n      formLabelWidth: '100px',\r\n      fullscreenLoading: false,\r\n      deptVisible: false,\r\n      qform: {\r\n        dept: null,\r\n        month: null\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    const user = this.$store.getters.user\r\n    this.deptVisible = user.isSuperAdmin || user.parentDept === '0'\r\n    this.search()\r\n  },\r\n  methods: {\r\n    colMonth(r, c, v) {\r\n      return v.substring(0, 4) + '年' + v.substring(4) + '月'\r\n    },\r\n    search() {\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    addBill() {\r\n      this.$refs.billCreate.show({})\r\n    },\r\n    editBill(data) {\r\n      this.fullscreenLoading = true\r\n      this.$http('/rp/zdj/zbt/get/' + data.id).then(res => {\r\n        this.fullscreenLoading = false\r\n        if (res.code > 0) {\r\n          this.$refs.billEdit.show(res.data)\r\n        }\r\n      }).catch(() => {\r\n        this.fullscreenLoading = false\r\n      })\r\n    },\r\n    checkBill(data) {\r\n      this.fullscreenLoading = true\r\n      this.$http('/rp/zdj/zbt/get/' + data.id).then(res => {\r\n        this.fullscreenLoading = false\r\n        if (res.code > 0) {\r\n          this.$refs.billCheck.show(res.data)\r\n        }\r\n      }).catch(() => {\r\n        this.fullscreenLoading = false\r\n      })\r\n    },\r\n    viewBill(data) {\r\n      this.fullscreenLoading = true\r\n      this.$http('/rp/zdj/zbt/get/' + data.id).then(res => {\r\n        this.fullscreenLoading = false\r\n        if (res.code > 0) {\r\n          this.$refs.billView.show(res.data)\r\n        }\r\n      }).catch(() => {\r\n        this.fullscreenLoading = false\r\n      })\r\n    },\r\n    exportBill(data) {\r\n      const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n      this.$jasper({ url: '/rp/zdj/zbt/export/' + data.id, responseType: 'blob' }).then(blob => {\r\n        loadInst.close()\r\n        this.$saveAs(blob, data.deptName + '_' + data.month + '.xlsx')\r\n      }).catch(err => {\r\n        loadInst.close()\r\n        this.$message.error('导出报表生成出错:' + err)\r\n      })\r\n    },\r\n    removeBill(data) {\r\n      this.$confirm('此操作将永久删除该报表, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.$http('/rp/zdj/zbt/delete/' + data.id).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('删除成功')\r\n            this.detailVisible = false\r\n            this.search()\r\n          }\r\n        })\r\n      }).catch(() => {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}