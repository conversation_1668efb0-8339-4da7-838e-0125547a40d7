{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\work\\ticai\\ticai-web\\src\\directive\\priv.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\directive\\priv.js", "mtime": 1753319668326}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js", "mtime": 1747730939696}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "store", "$_priv", "code", "user", "getters", "isSuperAdmin", "opts", "i", "length", "directive", "bind", "el", "binding", "vnode", "value", "style", "display", "inserted", "oldVnode", "updated", "prototype", "$priv", "deptType", "parentDept", "checkMenu", "menus", "menu", "find", "r", "children", "$_menu_priv", "menuCodes"], "sources": ["F:/work/ticai/ticai-web/src/directive/priv.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport store from '@/store'\r\n\r\nfunction $_priv(code) {\r\n  var user = store.getters.user\r\n  if (!user) return false\r\n  if (user.isSuperAdmin) return true\r\n  if (!user.opts) return false\r\n  for (var i = 0; i < user.opts.length; i++) {\r\n    if (user.opts[i] === code) return true\r\n  }\r\n  return false\r\n}\r\n\r\n// 操作权限\r\nVue.directive('priv', {\r\n  bind: function (el, binding, vnode) {\r\n    if (!$_priv(binding.value)) el.style.display = 'none'\r\n  },\r\n  inserted: function (el, binding, vnode, oldVnode) {\r\n  },\r\n  updated: function (el, binding, vnode, oldVnode) {\r\n  }\r\n})\r\n\r\nVue.prototype.$priv = $_priv\r\n\r\n// 省中心权限\r\nVue.directive('root-dept', {\r\n  bind: function (el, binding, vnode) {\r\n    var user = store.getters.user\r\n    if (!user || !(user.isSuperAdmin || user.deptType === '1' && user.parentDept === '0')) el.style.display = 'none'\r\n  },\r\n  inserted: function (el, binding, vnode, oldVnode) {\r\n  },\r\n  updated: function (el, binding, vnode, oldVnode) {\r\n  }\r\n})\r\n\r\nfunction checkMenu(menus, code) {\r\n  if (menus && menus.length) {\r\n    const menu = menus.find(r => r.code === code)\r\n    if (menu) return true\r\n    return checkMenu(menu.children, code)\r\n  }\r\n  return false\r\n}\r\n\r\n// 菜单权限\r\nfunction $_menu_priv(code) {\r\n  var user = store.getters.user\r\n  if (!user) return false\r\n  if (user.isSuperAdmin) return true\r\n  return checkMenu(user.menuCodes)\r\n}\r\n\r\n// 菜单权限\r\nVue.directive('menu-priv', {\r\n  bind: function (el, binding, vnode) {\r\n    if (!$_menu_priv(binding.value)) el.style.display = 'none'\r\n  },\r\n  inserted: function (el, binding, vnode, oldVnode) {\r\n  },\r\n  updated: function (el, binding, vnode, oldVnode) {\r\n  }\r\n})\r\n\r\nVue.prototype.$_menu_priv = $_menu_priv\r\n"], "mappings": ";;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,KAAK,MAAM,SAAS;AAE3B,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAIC,IAAI,GAAGH,KAAK,CAACI,OAAO,CAACD,IAAI;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;EACvB,IAAIA,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;EAClC,IAAI,CAACF,IAAI,CAACG,IAAI,EAAE,OAAO,KAAK;EAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACG,IAAI,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACzC,IAAIJ,IAAI,CAACG,IAAI,CAACC,CAAC,CAAC,KAAKL,IAAI,EAAE,OAAO,IAAI;EACxC;EACA,OAAO,KAAK;AACd;;AAEA;AACAH,GAAG,CAACU,SAAS,CAAC,MAAM,EAAE;EACpBC,IAAI,EAAE,SAANA,IAAIA,CAAYC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAClC,IAAI,CAACZ,MAAM,CAACW,OAAO,CAACE,KAAK,CAAC,EAAEH,EAAE,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;EACvD,CAAC;EACDC,QAAQ,EAAE,SAAVA,QAAQA,CAAYN,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEK,QAAQ,EAAE,CAClD,CAAC;EACDC,OAAO,EAAE,SAATA,OAAOA,CAAYR,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEK,QAAQ,EAAE,CACjD;AACF,CAAC,CAAC;AAEFnB,GAAG,CAACqB,SAAS,CAACC,KAAK,GAAGpB,MAAM;;AAE5B;AACAF,GAAG,CAACU,SAAS,CAAC,WAAW,EAAE;EACzBC,IAAI,EAAE,SAANA,IAAIA,CAAYC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAClC,IAAIV,IAAI,GAAGH,KAAK,CAACI,OAAO,CAACD,IAAI;IAC7B,IAAI,CAACA,IAAI,IAAI,EAAEA,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACmB,QAAQ,KAAK,GAAG,IAAInB,IAAI,CAACoB,UAAU,KAAK,GAAG,CAAC,EAAEZ,EAAE,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;EAClH,CAAC;EACDC,QAAQ,EAAE,SAAVA,QAAQA,CAAYN,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEK,QAAQ,EAAE,CAClD,CAAC;EACDC,OAAO,EAAE,SAATA,OAAOA,CAAYR,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEK,QAAQ,EAAE,CACjD;AACF,CAAC,CAAC;AAEF,SAASM,SAASA,CAACC,KAAK,EAAEvB,IAAI,EAAE;EAC9B,IAAIuB,KAAK,IAAIA,KAAK,CAACjB,MAAM,EAAE;IACzB,IAAMkB,IAAI,GAAGD,KAAK,CAACE,IAAI,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAAC1B,IAAI,KAAKA,IAAI;IAAA,EAAC;IAC7C,IAAIwB,IAAI,EAAE,OAAO,IAAI;IACrB,OAAOF,SAAS,CAACE,IAAI,CAACG,QAAQ,EAAE3B,IAAI,CAAC;EACvC;EACA,OAAO,KAAK;AACd;;AAEA;AACA,SAAS4B,WAAWA,CAAC5B,IAAI,EAAE;EACzB,IAAIC,IAAI,GAAGH,KAAK,CAACI,OAAO,CAACD,IAAI;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;EACvB,IAAIA,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;EAClC,OAAOmB,SAAS,CAACrB,IAAI,CAAC4B,SAAS,CAAC;AAClC;;AAEA;AACAhC,GAAG,CAACU,SAAS,CAAC,WAAW,EAAE;EACzBC,IAAI,EAAE,SAANA,IAAIA,CAAYC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAClC,IAAI,CAACiB,WAAW,CAAClB,OAAO,CAACE,KAAK,CAAC,EAAEH,EAAE,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;EAC5D,CAAC;EACDC,QAAQ,EAAE,SAAVA,QAAQA,CAAYN,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEK,QAAQ,EAAE,CAClD,CAAC;EACDC,OAAO,EAAE,SAATA,OAAOA,CAAYR,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEK,QAAQ,EAAE,CACjD;AACF,CAAC,CAAC;AAEFnB,GAAG,CAACqB,SAAS,CAACU,WAAW,GAAGA,WAAW", "ignoreList": []}]}