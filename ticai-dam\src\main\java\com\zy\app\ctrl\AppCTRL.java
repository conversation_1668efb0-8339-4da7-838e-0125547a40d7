package com.zy.app.ctrl;

import com.zy.app.req.qo.LoginData;
import com.zy.app.req.qo.UpdatePasswordData;
import com.zy.app.svc.AppSVC;
import com.zy.app.vo.User;
import com.zy.core.Context;
import com.zy.model.DataResult;
import com.zy.model.Result;
import com.zy.model.TypeKeyValue;
import com.zy.model.VueFile;
import com.zy.sys.api.SmsApi;
import com.zy.sys.orm.SysFile;
import com.zy.sys.svc.SysFileSVC;
import com.zy.util.ImageUtils;
import com.zy.util.RandomImage;
import com.zy.web.WebHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Tag(name = "系统全局接口")
@RestController
public class AppCTRL {

    private static final String VERIFY_CODE_PREFIX = "VC:";

    @Resource
    private AppSVC svc;

    @Resource
    private Context context;

    @Resource
    private RedisTemplate<String, String> rt;

    @Resource
    private SysFileSVC fileSVC;

    @Resource
    private SmsApi smsApi;

    @Value("${file.context:/attach}")
    private String fileContext;

    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public DataResult<User> login(@Parameter(description = "登录信息") @RequestBody LoginData data, @Parameter(hidden = true) HttpServletRequest request, @Parameter(hidden = true) HttpServletResponse response) {
        // 验证码
        String key = VERIFY_CODE_PREFIX + data.getVerifyId();
        BoundValueOperations<String, String> bo = rt.boundValueOps(key);
        String vc = bo.get();
        if (vc != null) rt.delete(key);
        if (vc == null || !vc.equals(data.getVerifyCode())) {
            return DataResult.err("请输入正确的验证码");
        }
        User user = svc.findForLogin(data.getAccount(), data.getPassword());
        if (user == null) return DataResult.err("帐号或密码出错");
        user.setLoginIp(WebHelper.getClientIP(request));
        context.bind(user, request, response);
        svc.loginSuccess(user);
        user.setAttachContext(fileContext);
        svc.append(user);
        if ("1".equals(user.getMp())) { // 更改密码要求
            return new DataResult<>(2, user);
        }
        return DataResult.data(user);
    }

    @Operation(summary = "用户注销")
    @PostMapping("/logout")
    public Result logout(@Parameter(hidden = true) HttpServletRequest request) {
        context.unbind(request);
        return Result.ok();
    }

    @Operation(summary = "获取系统配置信息")
    @GetMapping("/config")
    public DataResult<Map<String, Object>> getConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("superAdminAccount", svc.getSuperAdminAccount());
        return DataResult.data(config);
    }

    @Operation(summary = "验证码")
    @GetMapping("/verifyCode/{uid}")
    public void verifyCode(@Parameter(description = "唯一标识") @PathVariable("uid") String uid, @Parameter(description = "位数") Integer digit, @Parameter(description = "类型 : 或逻辑，1-数字，2-大写字母；4-小写字母") Integer type, @Parameter(hidden = true) HttpServletResponse response) {
        response.setContentType("image/gif");
        RandomImage ri = new RandomImage(digit != null && digit > 0 ? digit : 4, type != null && type > 0 ? type : 1, new Color(0x68, 0xb7, 0x1a), Color.white);
        ri.width = 90;
        ri.height = 33;
        BoundValueOperations<String, String> bo = rt.boundValueOps(VERIFY_CODE_PREFIX + uid);
        bo.set(ri.getCode());
        bo.expire(1, TimeUnit.MINUTES);
        try {
            javax.imageio.ImageIO.write(ri.getImage(), "gif", response.getOutputStream());
        } catch (IOException ignored) {
        }
    }

    @Operation(summary = "获取登录用户信息")
    @PostMapping("/user")
    public DataResult<User> getUser(@Parameter(hidden = true) User user) {
        if (user != null) {
            user.setAttachContext(fileContext);
            svc.append(user);
        }
        return DataResult.data(user);
    }

    @Operation(summary = "更新密码")
    @PostMapping("/updatePassword")
    public Result updatePassword(@Validated @RequestBody UpdatePasswordData data, @Parameter(hidden = true) User user) {
        return svc.updatePassword(user.getId(), data.getOldPassword(), data.getNewPassword());
    }

    @Operation(summary = "所有字典编码")
    @PostMapping("/code")
    public List<TypeKeyValue> findAllCode() {
        return svc.findAllCode();
    }

    /**
     * <p>
     * 上传，可剪切
     * </p>
     *
     * @param type 附件类型
     * @param file 附件数据
     * @return 结果
     */
    @PostMapping("/fileUpload/{type}")
    public Result fileUpload(@PathVariable("type") String type, MultipartFile file) {
        if (file == null || file.isEmpty()) return Result.err("请上传文件");
        String fileName = file.getOriginalFilename(), extName = null;
        if (fileName == null) return Result.err("请上传有效文件");
        int idx = fileName.lastIndexOf(".");
        if (idx > 0) {
            extName = fileName.substring(idx + 1);
        }
        try {
            SysFile sf = fileSVC.upload(type, file.getInputStream(), fileName, extName);
            if (sf == null) return Result.err("上传文件出错");
            VueFile vueFile = new VueFile();
            vueFile.setId(sf.getId());
            vueFile.setName(sf.getName());
            vueFile.setExt(sf.getExt());
            vueFile.setPath(sf.getPath());
            vueFile.setUrl(fileContext + sf.getPath());
            return Result.data(vueFile);
        } catch (IOException e) {
            return Result.err("无法读取文件");
        }
    }

    /**
     * @param type 附件类型
     * @param file 附件数据
     * @param rw   剪切宽度
     * @param rh   剪切高度
     * @param rx   剪切X偏移
     * @param ry   剪切Y偏移
     * @param tw   复制小图片宽度
     * @param th   复制小图片高度
     * @return 结果
     */
    @PostMapping("/imageUpload/{type}")
    public Result imageUpload(@PathVariable("type") String type, MultipartFile file, String formatName, Integer scaleWidth, Integer scaleHeight, Double rw, Double rh, Double rx, Double ry, Integer tw, Integer th) {
        if (file == null || file.isEmpty()) return Result.err("请上传文件");
        String fileName = file.getOriginalFilename(), extName = null;
        if (fileName == null) return Result.err("请上传有效文件");
        int idx = fileName.lastIndexOf(".");
        if (idx > 0) {
            extName = fileName.substring(idx + 1);
        }
        if (extName == null || extName.isEmpty()) extName = "jpg";
        if (formatName == null || formatName.isEmpty()) formatName = "png".equalsIgnoreCase(extName) ? "PNG" : "JPEG";
        SysFile sf;
        VueFile vueFile;
        byte[] imgBuf;
        byte[][] imgBufArr;
        try {
            if (scaleWidth != null && scaleWidth > 10) {
                if (scaleHeight == null || scaleHeight < 10) scaleHeight = scaleWidth;
                try {
                    imgBuf = ImageUtils.createResizedCopy(file.getInputStream(), scaleWidth, scaleHeight, formatName);
                    sf = fileSVC.upload(type, imgBuf, extName);
                    vueFile = new VueFile();
                    vueFile.setId(sf.getId());
                    vueFile.setName(sf.getName());
                    vueFile.setExt(sf.getExt());
                    vueFile.setPath(sf.getPath());
                    vueFile.setUrl(fileContext + sf.getPath());
                    return Result.data(vueFile);
                } catch (Exception ignored) {
                }
                return Result.err("上传出错");
            }
            if (tw != null && tw > 10 && th != null && th > 10) {
                if (rw != null && rh != null && rx != null && ry != null) {
                    imgBufArr = ImageUtils.crop(file.getInputStream(), formatName, rx, ry, rw, rh, tw, th);
                    if (imgBufArr.length == 0 || imgBufArr[0].length == 0) return Result.err("上传图片出错");
                    sf = fileSVC.upload(type, imgBufArr[0], extName); // 目标图片
                    if (imgBufArr.length > 1 && imgBufArr[1].length > 0) {
                        List<VueFile> vueFiles = new ArrayList<>();
                        vueFiles.add(vueFile = new VueFile());
                        vueFile.setId(sf.getId());
                        vueFile.setName(sf.getName());
                        vueFile.setExt(sf.getExt());
                        vueFile.setPath(sf.getPath());
                        vueFile.setUrl(fileContext + sf.getPath());
                        sf = fileSVC.upload(type + "_THUMB", imgBufArr[1], extName);
                        vueFiles.add(vueFile = new VueFile());
                        vueFile.setId(sf.getId());
                        vueFile.setName(sf.getName());
                        vueFile.setExt(sf.getExt());
                        vueFile.setPath(sf.getPath());
                        vueFile.setUrl(fileContext + sf.getPath());
                        return Result.data(vueFiles);
                    }
                    vueFile = new VueFile();
                    vueFile.setId(sf.getId());
                    vueFile.setName(sf.getName());
                    vueFile.setExt(sf.getExt());
                    vueFile.setPath(sf.getPath());
                    vueFile.setUrl(fileContext + sf.getPath());
                    return Result.data(vueFile);
                }
                sf = fileSVC.upload(type, file.getInputStream(), fileName, extName);
                if (sf == null) return Result.data("上传出错");
                List<VueFile> vueFiles = new ArrayList<>();
                vueFiles.add(vueFile = new VueFile());
                vueFile.setId(sf.getId());
                vueFile.setName(sf.getName());
                vueFile.setExt(sf.getExt());
                vueFile.setPath(sf.getPath());
                vueFile.setUrl(fileContext + sf.getPath());
                FileInputStream ins = null;
                try {
                    ins = new FileInputStream(sf.getRealPath());
                    imgBuf = ImageUtils.createResizedCopy(ins, tw, th, formatName);
                    sf = fileSVC.upload(type + "_THUMB", imgBuf, extName);
                    vueFiles.add(vueFile = new VueFile());
                    vueFile.setId(sf.getId());
                    vueFile.setName(sf.getName());
                    vueFile.setExt(sf.getExt());
                    vueFile.setPath(sf.getPath());
                    vueFile.setUrl(fileContext + sf.getPath());
                } catch (Exception ignored) {
                } finally {
                    if (ins != null) {
                        try {
                            ins.close();
                        } catch (Exception ignored) {
                        }
                    }
                }
                return Result.data(vueFiles);
            } else {
                if (rw != null && rh != null && rx != null && ry != null) {
                    sf = fileSVC.upload(type, com.zy.util.ImageUtils.crop(file.getInputStream(), formatName, rx, ry, rw, rh), "jpg");
                } else {
                    sf = fileSVC.upload(type, file.getInputStream(), fileName, extName);
                }
            }
            vueFile = new VueFile();
            vueFile.setId(sf.getId());
            vueFile.setName(sf.getName());
            vueFile.setExt(sf.getExt());
            vueFile.setPath(sf.getPath());
            vueFile.setUrl(fileContext + sf.getPath());
            return Result.data(vueFile);
        } catch (IOException ignored) {
            return Result.err("无法读取文件");
        }
    }


    /**
     * vue文件列表
     *
     * @param type 附件类型
     * @param rid  附件业务ID
     * @return 结果
     */
    @PostMapping("/fileList/{type}/{rid}")
    public List<VueFile> fileList(@PathVariable("type") String type, @PathVariable("rid") String rid) {
        List<VueFile> files = fileSVC.findVueFileByRid(type, rid);
        for (VueFile file : files) file.setUrl(fileContext + file.getPath());
        return files;
    }

    /**
     * 移除文件
     *
     * @param id 附件ID
     * @return 结果
     */
    @PostMapping("/fileRemove/{id}")
    public Result fileRemove(@PathVariable("id") String id) {
        return fileSVC.remove(id);
    }

    /**
     * 获取附件上下文
     *
     * @return 附件上下文
     */
    @PostMapping("/fileContext")
    public Result getFileContext() {
        return Result.data(fileContext);
    }


    /**
     * 消息数量
     *
     * @return 结果
     */
    @PostMapping("/messageCount")
    public Result getMessageCount() {
        return Result.data(0);
    }


    @GetMapping("/sendSm")
    public Result sendSm(@RequestParam("mobile") String mobile, @RequestParam("code") String code, User user) {
        smsApi.send(mobile, code, user.getId(), "1", "");
        return Result.ok();
    }
    
}
