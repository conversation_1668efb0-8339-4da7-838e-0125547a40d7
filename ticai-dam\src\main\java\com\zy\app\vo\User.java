package com.zy.app.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zy.model.BaseUser;
import com.zy.model.TypeKeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "登录用户信息")
public class User extends BaseUser {

    /**
     * 更改密码要求：0-不需要，1-需要
     */
    @Schema(description = "更改密码要求：0-不需要，1-需要")
    private String mp;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色ID列表")
    private String roleIds;

    @Schema(description = "菜单列表")
    private List<Menu> menus;

    @Schema(description = "操作按钮列表")
    private List<String> opts;

    @Schema(description = "上级机构")
    private String parentDept;

    @Schema(description = "类型：1-中心，2-维保，3-使用业主")
    private String deptType;

    @Schema(description = "包含机构ID列表")
    private List<String> deptIds;

    @Schema(description = "权限编码")
    private String privilege;

    @Schema(description = "附加权限编码列表")
    private String[] otherPrivileges;

    @Schema(description = "所有字典列表，登录后携带，不存入redis")
    private List<TypeKeyValue> dictCodes;

    @Schema(description = "附件上下文")
    private String attachContext;

    @Schema(description = "是否为超级管理员")
    private Boolean isSuperAdmin;

    @Schema(hidden = true)
    @JsonIgnore
    public boolean isAdmin() {
        return isSuperAdmin != null && isSuperAdmin;
    }
}
