<template>
  <el-container class="page-table-ctn">
    <!-- <el-popover v-if="columns.length > 0" width="60" trigger="click">
      <el-checkbox-group v-model="columns">
        <el-checkbox v-for="item in columns" :key="item.index" :label="item.title" />
      </el-checkbox-group>
      <el-button slot="reference" icon="el-icon-more" circle style="margin-left: 100px;"></el-button>
    </el-popover> -->
    <el-table ref="grid" v-loading="loading" :max-height="maxHeight" :data="rows" v-bind="$attrs" v-on="$listeners">
      <slot></slot>
    </el-table>
    <el-footer v-if="paging" class="footer">
      <div class="size-info">
        <span v-if="total > 1">显示第 {{ from }} 条到第 {{ to }} 条的数据，</span> 共{{ total }} 条数据
      </div>
      <el-pagination
        style="float:right"
        :layout="layout"
        :page-sizes="pageSizes"
        :current-page="pi"
        :page-size="pz"
        :total="total"
        v-bind="$attrs"
        @current-change="handleNumberChange"
        @size-change="handleSizeChange"
      ></el-pagination>
    </el-footer>
  </el-container>
</template>

<script>

import request from '../../utils/request'

export default {
  name: 'PageTable',
  props: {
    path: {
      type: String,
      require: true,
      default: null
    },
    pageSize: {
      type: Number,
      default: 10
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 30, 50, 100]
    },
    layout: {
      type: String,
      default: 'sizes, prev, pager, next, jumper'
    },
    paging: { // 是否分页，默认为true，即分页。（不分页时将每页条数设置最大。）
      type: Boolean,
      default: true
    },
    query: { // 初始化参数
      type: Object,
      default: () => {}
    },
    auto: { // 自动查询
      type: Boolean,
      default: false
    },
    checkField: {
      type: String,
      default: null
    }
    // columns: {
    //   type: Array,
    //   default: () => []
    // }
  },
  data() {
    return {
      pi: 1, // 页标
      pz: this.pageSize, // 页长
      params: this.query || {},
      rows: [],
      total: 0,
      from: 0,
      to: 0,
      maxHeight: null,
      loading: false
    }
  },
  watch: {
    // columnSelecteds(newArrayVal) {
    //   console.log(newArrayVal)
    //   // 计算为被选中的列标题数组
    //   var nonSelecteds = this.columns.filter(item => newArrayVal.indexOf(item.index) === -1).map(item => item.index)
    //   this.columns.filter(item => {
    //     const isNonSelected = nonSelecteds.indexOf(item.index) !== -1
    //     if (isNonSelected) {
    //       // 隐藏未选中的列
    //       item.visible = false
    //       this.$nextTick(() => {
    //         this.$refs.grid.doLayout()
    //       })
    //     } else {
    //       // 显示已选中的列
    //       item.visible = true
    //       this.$nextTick(() => {
    //         this.$refs.grid.doLayout()
    //       })
    //     }
    //   })
    // }
  },
  mounted() {
    if (this.auto) this.search()
  },
  methods: {
    setParams(value) {
      this.params = value || {}
    },
    setMaxHeight(value) {
      this.maxHeight = value
      this.$refs.grid.doLayout()
    },
    handleSizeChange(value) {
      this.pz = value
      this.search(1)
    },
    handleNumberChange(value) {
      this.search(value)
    },
    search(arg, a) {
      if (!this.path) return
      const ps = { pageNumber: 1 }
      const argType = typeof arg
      if (argType === 'undefined') ps.pageNumber = 1
      else if (argType === 'number') ps.pageNumber = arg
      else if (argType === 'object') {
        this.params = arg
        if (typeof a === 'number') ps.pageNumber = a // 指定页码
        if (typeof a === 'boolean') this.empty() // 查询前清空
      } else ps.pageNumber = arg.pageNumber
      this.pi = ps.pageNumber
      if (this.paging) {
        this.params.pageNumber = ps.pageNumber
        this.params.pageSize = this.pz
      }
      this.loading = true
      request({
        url: this.path,
        data: this.params
      }).then(res => {
        this.loading = false
        if (this.paging) this.renderPage(res)
        else this.renderList(res.rows ? res.rows : res)
        this.$emit('loaded', res) // 加载数据返回
      }).catch(err => {
        this.loading = false
        console.log(err)
      })
    },
    empty() {
      this.pi = 1
      this.rows = []
      this.total = 0
      this.from = 0
      this.to = 0
    },
    renderList(res) {
      this.rows = res
    },
    renderPage(res) {
      if (this.checkField) res.rows.forEach(r => { r[this.checkField] = false })
      this.rows = res.rows
      this.total = res.total
      if (this.total > 0) {
        this.from = (this.pi - 1) * this.pz + 1
        this.to = this.from + (this.rows && this.rows.length ? this.rows.length - 1 : 0)
      } else {
        this.from = 0
        this.to = 0
      }
    },
    getData() {
      return this.rows
    },
    getSelection() {
      return this.$refs.grid.selection
    },
    getSelectionId(field) {
      const items = this.$refs.grid.selection
      if (!field) field = 'id'
      const ids = []
      for (let i = 0; i < items.length; i++) {
        if (items[i][field]) ids.push(items[i][field])
      }
      return ids
    }
  }
}
</script>

<style lang="scss" scoped>
.page-table-ctn {
  > .el-table {
    width: '100%';
    margin-bottom: 14px;
    border: 1px solid #ebeef5;
    border-bottom: unset;
  }
  > .footer {
    height: 40px !important;
    .size-info {
      display: inline;
      font-size: 12px;
      color: #666666;
    }
  }
}
</style>
