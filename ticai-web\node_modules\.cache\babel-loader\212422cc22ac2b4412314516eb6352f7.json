{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\work\\ticai\\ticai-web\\src\\directive\\part.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\directive\\part.js", "mtime": 1753320962831}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js", "mtime": 1747730939696}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZSc7CmZ1bmN0aW9uICRfcGFydChjb2RlKSB7CiAgdmFyIHVzZXIgPSBzdG9yZS5nZXR0ZXJzLnVzZXI7CiAgaWYgKCF1c2VyKSByZXR1cm4gZmFsc2U7CiAgaWYgKHVzZXIuaXNTdXBlckFkbWluKSByZXR1cm4gdHJ1ZTsKICBpZiAoIXVzZXIuZGVwdFR5cGUpIHJldHVybiBmYWxzZTsKICBpZiAoY29kZS5pbmRleE9mKHVzZXIuZGVwdFR5cGUpID49IDApIHJldHVybiB0cnVlOwogIHJldHVybiBmYWxzZTsKfQoKLy8g5pON5L2c5p2D6ZmQClZ1ZS5kaXJlY3RpdmUoJ3BhcnQnLCB7CiAgYmluZDogZnVuY3Rpb24gYmluZChlbCwgYmluZGluZywgdm5vZGUpIHt9LAogIGluc2VydGVkOiBmdW5jdGlvbiBpbnNlcnRlZChlbCwgYmluZGluZywgdm5vZGUsIG9sZFZub2RlKSB7CiAgICAvLyBpZiAoISRfcGFydChiaW5kaW5nLnZhbHVlKSkgZWwucGFyZW50Tm9kZS5yZW1vdmVDaGlsZChlbCkKICAgIC8vIGlmICghJF9wYXJ0KGJpbmRpbmcudmFsdWUpKSBlbC5yZW1vdmUoKQogICAgaWYgKCEkX3BhcnQoYmluZGluZy52YWx1ZSkpIHsKICAgICAgZWwucGFyZW50Tm9kZSAmJiBlbC5wYXJlbnROb2RlLnJlbW92ZUNoaWxkKGVsKTsKICAgIH0KICB9LAogIHVwZGF0ZWQ6IGZ1bmN0aW9uIHVwZGF0ZWQoZWwsIGJpbmRpbmcsIHZub2RlLCBvbGRWbm9kZSkgewogICAgLy8gY29uc29sZS5sb2coZWwsIGJpbmRpbmcpCgogICAgLy8gaWYgKCEkX3BhcnQoYmluZGluZy52YWx1ZSkpIGVsLnJlbW92ZSgpCiAgfQp9KTsKVnVlLnByb3RvdHlwZS4kcGFydCA9ICRfcGFydDs="}, {"version": 3, "names": ["<PERSON><PERSON>", "store", "$_part", "code", "user", "getters", "isSuperAdmin", "deptType", "indexOf", "directive", "bind", "el", "binding", "vnode", "inserted", "oldVnode", "value", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "updated", "prototype", "$part"], "sources": ["F:/work/ticai/ticai-web/src/directive/part.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport store from '@/store'\r\n\r\nfunction $_part(code) {\r\n  const user = store.getters.user\r\n  if (!user) return false\r\n  if (user.isSuperAdmin) return true\r\n  if (!user.deptType) return false\r\n  if (code.indexOf(user.deptType) >= 0) return true\r\n  return false\r\n}\r\n\r\n// 操作权限\r\nVue.directive('part', {\r\n  bind: function (el, binding, vnode) {\r\n\r\n  },\r\n  inserted: function (el, binding, vnode, oldVnode) {\r\n    // if (!$_part(binding.value)) el.parentNode.removeChild(el)\r\n    // if (!$_part(binding.value)) el.remove()\r\n    if (!$_part(binding.value)) {\r\n      el.parentNode && el.parentNode.removeChild(el)\r\n    }\r\n  },\r\n  updated: function (el, binding, vnode, oldVnode) {\r\n    // console.log(el, binding)\r\n\r\n    // if (!$_part(binding.value)) el.remove()\r\n  }\r\n})\r\n\r\nVue.prototype.$part = $_part\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,KAAK,MAAM,SAAS;AAE3B,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAMC,IAAI,GAAGH,KAAK,CAACI,OAAO,CAACD,IAAI;EAC/B,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;EACvB,IAAIA,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;EAClC,IAAI,CAACF,IAAI,CAACG,QAAQ,EAAE,OAAO,KAAK;EAChC,IAAIJ,IAAI,CAACK,OAAO,CAACJ,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI;EACjD,OAAO,KAAK;AACd;;AAEA;AACAP,GAAG,CAACS,SAAS,CAAC,MAAM,EAAE;EACpBC,IAAI,EAAE,SAANA,IAAIA,CAAYC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE,CAEpC,CAAC;EACDC,QAAQ,EAAE,SAAVA,QAAQA,CAAYH,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEE,QAAQ,EAAE;IAChD;IACA;IACA,IAAI,CAACb,MAAM,CAACU,OAAO,CAACI,KAAK,CAAC,EAAE;MAC1BL,EAAE,CAACM,UAAU,IAAIN,EAAE,CAACM,UAAU,CAACC,WAAW,CAACP,EAAE,CAAC;IAChD;EACF,CAAC;EACDQ,OAAO,EAAE,SAATA,OAAOA,CAAYR,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEE,QAAQ,EAAE;IAC/C;;IAEA;EAAA;AAEJ,CAAC,CAAC;AAEFf,GAAG,CAACoB,SAAS,CAACC,KAAK,GAAGnB,MAAM", "ignoreList": []}]}