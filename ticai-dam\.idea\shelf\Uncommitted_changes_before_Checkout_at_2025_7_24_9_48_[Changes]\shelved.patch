Index: src/main/resources/application-zl.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>server:\r\n  port: 20000\r\nspring:\r\n  datasource-master:\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    jdbc-url: *************************************************************************************************************************************************    username: root\r\n    password: BCA27CFBFDDDE195\r\n    initial-size: 10 #初始化连接\r\n    max-idle: 20 #最大空闲连接\r\n    min-idle: 5 #最小空闲连接\r\n    max-active: 200 #最大连接数量\r\n    log-abandoned: true #是否在自动回收超时连接的时候打印连接的超时错误\r\n    remove-abandoned: true #是否自动回收超时连接\r\n    remove-abandoned-timeout: 180 #超时时间(以秒数为单位)\r\n    max-wait: 10000 #超时等待时间以毫秒为单位 6000毫秒/1000等于60秒\r\n    test-while-idle: true\r\n    connection-test-query: select now()  #检测数据库的查询语句\r\n    test-on-borrow: true\r\n    min-evictable-idle-time-millis: 600000 #每隔五分钟检测空闲超过10分钟的连接\r\n    time-between-eviction-runs-millis: 300000\r\n    maximum-pool-size: 50  #池中最大连接数（包括空闲和正在使用的连接）默认值是10\r\n    minimum-idle: 10  #池中最小空闲连接数量。默认值10\r\n    pool-name: zy-ds-pool  #连接池的名字。\r\n    auto-commit: true  #是否自动提交池中返回的连接。默认值为true\r\n    idle-timeout: 10000  #空闲时间，毫秒\r\n    max-lifetime: 500000 #连接池中连接的最大生命周期。\r\n    connection-timeout: 20000  #连接超时时间，毫秒。默认值为30s\r\n\r\n  redis:\r\n    database: 0 #数据库索引（默认为0）\r\n    host: 127.0.0.1\r\n    port: 6378\r\n    password: xudejian\r\n    max-active: 500 #连接池最大连接数（使用负值表示没有限制）\r\n    max-wait: -1 #连接池最大阻塞等待时间（使用负值表示没有限制）\r\n    max-idle: 20 #连接池中的最大空闲连接\r\n    min-idle: 10 #连接池中的最小空闲连接\r\n    timeout: 5000 #连接超时时间（毫秒）\r\n\r\nfile:\r\n  path: E:\\\\Attach\r\n  context: /attach\r\n\r\nlogging:\r\n  level:\r\n    org.springframework.boot.autoconfigure: INFO\r\n\r\n# 注册中心\r\neureka:\r\n  client:\r\n    enabled: false\r\n    serviceUrl:\r\n      defaultZone: **************************************/eureka/\r\n    tls:\r\n      trust-store-password: abc\r\n  instance:\r\n    leaseRenewalIntervalInSeconds: 10\r\n    metadataMap:\r\n      instanceId: ${vcap.application.instance_id:${spring.application.name}:${spring.application.instance_id:${server.port}}}\r\n\r\nweixin:\r\n  appid: \"wxd8fbf8e2f5fc0a3a\"\r\n  secret: \"65b46f1e57d810989a1040d703668a3b\"\r\n\r\namap:\r\n  key: \"b4ae4625e7f217801a29af656cbf8ce7\"
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/resources/application-zl.yml b/src/main/resources/application-zl.yml
--- a/src/main/resources/application-zl.yml	(revision 675c1b730e42e2e479759d13ea62c9a740e178fb)
+++ b/src/main/resources/application-zl.yml	(date 1753321308927)
@@ -3,9 +3,9 @@
 spring:
   datasource-master:
     driver-class-name: com.mysql.cj.jdbc.Driver
-    jdbc-url: *********************************************************************************************************************************************
+    jdbc-url: jdbc:mysql://**************:53306/dam?serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=true
     username: root
-    password: BCA27CFBFDDDE195
+    password: XX2U5nxQYeMyQkOA!!
     initial-size: 10 #初始化连接
     max-idle: 20 #最大空闲连接
     min-idle: 5 #最小空闲连接
@@ -30,8 +30,7 @@
   redis:
     database: 0 #数据库索引（默认为0）
     host: 127.0.0.1
-    port: 6378
-    password: xudejian
+    port: 6379
     max-active: 500 #连接池最大连接数（使用负值表示没有限制）
     max-wait: -1 #连接池最大阻塞等待时间（使用负值表示没有限制）
     max-idle: 20 #连接池中的最大空闲连接
Index: src/main/java/com/zy/app/ctrl/AppCTRL.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.zy.app.ctrl;\r\n\r\nimport com.zy.app.req.qo.LoginData;\r\nimport com.zy.app.req.qo.UpdatePasswordData;\r\nimport com.zy.app.svc.AppSVC;\r\nimport com.zy.app.vo.User;\r\nimport com.zy.core.Context;\r\nimport com.zy.model.DataResult;\r\nimport com.zy.model.Result;\r\nimport com.zy.model.TypeKeyValue;\r\nimport com.zy.model.VueFile;\r\nimport com.zy.sys.api.SmsApi;\r\nimport com.zy.sys.orm.SysFile;\r\nimport com.zy.sys.svc.SysFileSVC;\r\nimport com.zy.util.ImageUtils;\r\nimport com.zy.util.RandomImage;\r\nimport com.zy.web.WebHelper;\r\nimport io.swagger.v3.oas.annotations.Operation;\r\nimport io.swagger.v3.oas.annotations.Parameter;\r\nimport io.swagger.v3.oas.annotations.tags.Tag;\r\nimport org.springframework.beans.factory.annotation.Value;\r\nimport org.springframework.data.redis.core.BoundValueOperations;\r\nimport org.springframework.data.redis.core.RedisTemplate;\r\nimport org.springframework.validation.annotation.Validated;\r\nimport org.springframework.web.bind.annotation.*;\r\nimport org.springframework.web.multipart.MultipartFile;\r\n\r\nimport javax.annotation.Resource;\r\nimport javax.servlet.http.HttpServletRequest;\r\nimport javax.servlet.http.HttpServletResponse;\r\nimport java.awt.*;\r\nimport java.io.FileInputStream;\r\nimport java.io.IOException;\r\nimport java.util.ArrayList;\r\nimport java.util.List;\r\nimport java.util.concurrent.TimeUnit;\r\n\r\n@Tag(name = \"系统全局接口\")\r\n@RestController\r\npublic class AppCTRL {\r\n\r\n    private static final String VERIFY_CODE_PREFIX = \"VC:\";\r\n\r\n    @Resource\r\n    private AppSVC svc;\r\n\r\n    @Resource\r\n    private Context context;\r\n\r\n    @Resource\r\n    private RedisTemplate<String, String> rt;\r\n\r\n    @Resource\r\n    private SysFileSVC fileSVC;\r\n\r\n    @Resource\r\n    private SmsApi smsApi;\r\n\r\n    @Value(\"${file.context:/attach}\")\r\n    private String fileContext;\r\n\r\n    @Operation(summary = \"用户登录\")\r\n    @PostMapping(\"/login\")\r\n    public DataResult<User> login(@Parameter(description = \"登录信息\") @RequestBody LoginData data, @Parameter(hidden = true) HttpServletRequest request, @Parameter(hidden = true) HttpServletResponse response) {\r\n        // 验证码\r\n        String key = VERIFY_CODE_PREFIX + data.getVerifyId();\r\n        BoundValueOperations<String, String> bo = rt.boundValueOps(key);\r\n        String vc = bo.get();\r\n        if (vc != null) rt.delete(key);\r\n        if (vc == null || !vc.equals(data.getVerifyCode())) {\r\n            return DataResult.err(\"请输入正确的验证码\");\r\n        }\r\n        User user = svc.findForLogin(data.getAccount(), data.getPassword());\r\n        if (user == null) return DataResult.err(\"帐号或密码出错\");\r\n        user.setLoginIp(WebHelper.getClientIP(request));\r\n        context.bind(user, request, response);\r\n        svc.loginSuccess(user);\r\n        user.setAttachContext(fileContext);\r\n        svc.append(user);\r\n        if (\"1\".equals(user.getMp())) { // 更改密码要求\r\n            return new DataResult<>(2, user);\r\n        }\r\n        return DataResult.data(user);\r\n    }\r\n\r\n    @Operation(summary = \"用户注销\")\r\n    @PostMapping(\"/logout\")\r\n    public Result logout(@Parameter(hidden = true) HttpServletRequest request) {\r\n        context.unbind(request);\r\n        return Result.ok();\r\n    }\r\n\r\n    @Operation(summary = \"验证码\")\r\n    @GetMapping(\"/verifyCode/{uid}\")\r\n    public void verifyCode(@Parameter(description = \"唯一标识\") @PathVariable(\"uid\") String uid, @Parameter(description = \"位数\") Integer digit, @Parameter(description = \"类型 : 或逻辑，1-数字，2-大写字母；4-小写字母\") Integer type, @Parameter(hidden = true) HttpServletResponse response) {\r\n        response.setContentType(\"image/gif\");\r\n        RandomImage ri = new RandomImage(digit != null && digit > 0 ? digit : 4, type != null && type > 0 ? type : 1, new Color(0x68, 0xb7, 0x1a), Color.white);\r\n        ri.width = 90;\r\n        ri.height = 33;\r\n        BoundValueOperations<String, String> bo = rt.boundValueOps(VERIFY_CODE_PREFIX + uid);\r\n        bo.set(ri.getCode());\r\n        bo.expire(1, TimeUnit.MINUTES);\r\n        try {\r\n            javax.imageio.ImageIO.write(ri.getImage(), \"gif\", response.getOutputStream());\r\n        } catch (IOException ignored) {\r\n        }\r\n    }\r\n\r\n    @Operation(summary = \"获取登录用户信息\")\r\n    @PostMapping(\"/user\")\r\n    public DataResult<User> getUser(@Parameter(hidden = true) User user) {\r\n        if (user != null) {\r\n            user.setAttachContext(fileContext);\r\n            svc.append(user);\r\n        }\r\n        return DataResult.data(user);\r\n    }\r\n\r\n    @Operation(summary = \"更新密码\")\r\n    @PostMapping(\"/updatePassword\")\r\n    public Result updatePassword(@Validated @RequestBody UpdatePasswordData data, @Parameter(hidden = true) User user) {\r\n        return svc.updatePassword(user.getId(), data.getOldPassword(), data.getNewPassword());\r\n    }\r\n\r\n    @Operation(summary = \"所有字典编码\")\r\n    @PostMapping(\"/code\")\r\n    public List<TypeKeyValue> findAllCode() {\r\n        return svc.findAllCode();\r\n    }\r\n\r\n    /**\r\n     * <p>\r\n     * 上传，可剪切\r\n     * </p>\r\n     *\r\n     * @param type 附件类型\r\n     * @param file 附件数据\r\n     * @return 结果\r\n     */\r\n    @PostMapping(\"/fileUpload/{type}\")\r\n    public Result fileUpload(@PathVariable(\"type\") String type, MultipartFile file) {\r\n        if (file == null || file.isEmpty()) return Result.err(\"请上传文件\");\r\n        String fileName = file.getOriginalFilename(), extName = null;\r\n        if (fileName == null) return Result.err(\"请上传有效文件\");\r\n        int idx = fileName.lastIndexOf(\".\");\r\n        if (idx > 0) {\r\n            extName = fileName.substring(idx + 1);\r\n        }\r\n        try {\r\n            SysFile sf = fileSVC.upload(type, file.getInputStream(), fileName, extName);\r\n            if (sf == null) return Result.err(\"上传文件出错\");\r\n            VueFile vueFile = new VueFile();\r\n            vueFile.setId(sf.getId());\r\n            vueFile.setName(sf.getName());\r\n            vueFile.setExt(sf.getExt());\r\n            vueFile.setPath(sf.getPath());\r\n            vueFile.setUrl(fileContext + sf.getPath());\r\n            return Result.data(vueFile);\r\n        } catch (IOException e) {\r\n            return Result.err(\"无法读取文件\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @param type 附件类型\r\n     * @param file 附件数据\r\n     * @param rw   剪切宽度\r\n     * @param rh   剪切高度\r\n     * @param rx   剪切X偏移\r\n     * @param ry   剪切Y偏移\r\n     * @param tw   复制小图片宽度\r\n     * @param th   复制小图片高度\r\n     * @return 结果\r\n     */\r\n    @PostMapping(\"/imageUpload/{type}\")\r\n    public Result imageUpload(@PathVariable(\"type\") String type, MultipartFile file, String formatName, Integer scaleWidth, Integer scaleHeight, Double rw, Double rh, Double rx, Double ry, Integer tw, Integer th) {\r\n        if (file == null || file.isEmpty()) return Result.err(\"请上传文件\");\r\n        String fileName = file.getOriginalFilename(), extName = null;\r\n        if (fileName == null) return Result.err(\"请上传有效文件\");\r\n        int idx = fileName.lastIndexOf(\".\");\r\n        if (idx > 0) {\r\n            extName = fileName.substring(idx + 1);\r\n        }\r\n        if (extName == null || extName.isEmpty()) extName = \"jpg\";\r\n        if (formatName == null || formatName.isEmpty()) formatName = \"png\".equalsIgnoreCase(extName) ? \"PNG\" : \"JPEG\";\r\n        SysFile sf;\r\n        VueFile vueFile;\r\n        byte[] imgBuf;\r\n        byte[][] imgBufArr;\r\n        try {\r\n            if (scaleWidth != null && scaleWidth > 10) {\r\n                if (scaleHeight == null || scaleHeight < 10) scaleHeight = scaleWidth;\r\n                try {\r\n                    imgBuf = ImageUtils.createResizedCopy(file.getInputStream(), scaleWidth, scaleHeight, formatName);\r\n                    sf = fileSVC.upload(type, imgBuf, extName);\r\n                    vueFile = new VueFile();\r\n                    vueFile.setId(sf.getId());\r\n                    vueFile.setName(sf.getName());\r\n                    vueFile.setExt(sf.getExt());\r\n                    vueFile.setPath(sf.getPath());\r\n                    vueFile.setUrl(fileContext + sf.getPath());\r\n                    return Result.data(vueFile);\r\n                } catch (Exception ignored) {\r\n                }\r\n                return Result.err(\"上传出错\");\r\n            }\r\n            if (tw != null && tw > 10 && th != null && th > 10) {\r\n                if (rw != null && rh != null && rx != null && ry != null) {\r\n                    imgBufArr = ImageUtils.crop(file.getInputStream(), formatName, rx, ry, rw, rh, tw, th);\r\n                    if (imgBufArr.length == 0 || imgBufArr[0].length == 0) return Result.err(\"上传图片出错\");\r\n                    sf = fileSVC.upload(type, imgBufArr[0], extName); // 目标图片\r\n                    if (imgBufArr.length > 1 && imgBufArr[1].length > 0) {\r\n                        List<VueFile> vueFiles = new ArrayList<>();\r\n                        vueFiles.add(vueFile = new VueFile());\r\n                        vueFile.setId(sf.getId());\r\n                        vueFile.setName(sf.getName());\r\n                        vueFile.setExt(sf.getExt());\r\n                        vueFile.setPath(sf.getPath());\r\n                        vueFile.setUrl(fileContext + sf.getPath());\r\n                        sf = fileSVC.upload(type + \"_THUMB\", imgBufArr[1], extName);\r\n                        vueFiles.add(vueFile = new VueFile());\r\n                        vueFile.setId(sf.getId());\r\n                        vueFile.setName(sf.getName());\r\n                        vueFile.setExt(sf.getExt());\r\n                        vueFile.setPath(sf.getPath());\r\n                        vueFile.setUrl(fileContext + sf.getPath());\r\n                        return Result.data(vueFiles);\r\n                    }\r\n                    vueFile = new VueFile();\r\n                    vueFile.setId(sf.getId());\r\n                    vueFile.setName(sf.getName());\r\n                    vueFile.setExt(sf.getExt());\r\n                    vueFile.setPath(sf.getPath());\r\n                    vueFile.setUrl(fileContext + sf.getPath());\r\n                    return Result.data(vueFile);\r\n                }\r\n                sf = fileSVC.upload(type, file.getInputStream(), fileName, extName);\r\n                if (sf == null) return Result.data(\"上传出错\");\r\n                List<VueFile> vueFiles = new ArrayList<>();\r\n                vueFiles.add(vueFile = new VueFile());\r\n                vueFile.setId(sf.getId());\r\n                vueFile.setName(sf.getName());\r\n                vueFile.setExt(sf.getExt());\r\n                vueFile.setPath(sf.getPath());\r\n                vueFile.setUrl(fileContext + sf.getPath());\r\n                FileInputStream ins = null;\r\n                try {\r\n                    ins = new FileInputStream(sf.getRealPath());\r\n                    imgBuf = ImageUtils.createResizedCopy(ins, tw, th, formatName);\r\n                    sf = fileSVC.upload(type + \"_THUMB\", imgBuf, extName);\r\n                    vueFiles.add(vueFile = new VueFile());\r\n                    vueFile.setId(sf.getId());\r\n                    vueFile.setName(sf.getName());\r\n                    vueFile.setExt(sf.getExt());\r\n                    vueFile.setPath(sf.getPath());\r\n                    vueFile.setUrl(fileContext + sf.getPath());\r\n                } catch (Exception ignored) {\r\n                } finally {\r\n                    if (ins != null) {\r\n                        try {\r\n                            ins.close();\r\n                        } catch (Exception ignored) {\r\n                        }\r\n                    }\r\n                }\r\n                return Result.data(vueFiles);\r\n            } else {\r\n                if (rw != null && rh != null && rx != null && ry != null) {\r\n                    sf = fileSVC.upload(type, com.zy.util.ImageUtils.crop(file.getInputStream(), formatName, rx, ry, rw, rh), \"jpg\");\r\n                } else {\r\n                    sf = fileSVC.upload(type, file.getInputStream(), fileName, extName);\r\n                }\r\n            }\r\n            vueFile = new VueFile();\r\n            vueFile.setId(sf.getId());\r\n            vueFile.setName(sf.getName());\r\n            vueFile.setExt(sf.getExt());\r\n            vueFile.setPath(sf.getPath());\r\n            vueFile.setUrl(fileContext + sf.getPath());\r\n            return Result.data(vueFile);\r\n        } catch (IOException ignored) {\r\n            return Result.err(\"无法读取文件\");\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * vue文件列表\r\n     *\r\n     * @param type 附件类型\r\n     * @param rid  附件业务ID\r\n     * @return 结果\r\n     */\r\n    @PostMapping(\"/fileList/{type}/{rid}\")\r\n    public List<VueFile> fileList(@PathVariable(\"type\") String type, @PathVariable(\"rid\") String rid) {\r\n        List<VueFile> files = fileSVC.findVueFileByRid(type, rid);\r\n        for (VueFile file : files) file.setUrl(fileContext + file.getPath());\r\n        return files;\r\n    }\r\n\r\n    /**\r\n     * 移除文件\r\n     *\r\n     * @param id 附件ID\r\n     * @return 结果\r\n     */\r\n    @PostMapping(\"/fileRemove/{id}\")\r\n    public Result fileRemove(@PathVariable(\"id\") String id) {\r\n        return fileSVC.remove(id);\r\n    }\r\n\r\n    /**\r\n     * 获取附件上下文\r\n     *\r\n     * @return 附件上下文\r\n     */\r\n    @PostMapping(\"/fileContext\")\r\n    public Result getFileContext() {\r\n        return Result.data(fileContext);\r\n    }\r\n\r\n\r\n    /**\r\n     * 消息数量\r\n     *\r\n     * @return 结果\r\n     */\r\n    @PostMapping(\"/messageCount\")\r\n    public Result getMessageCount() {\r\n        return Result.data(0);\r\n    }\r\n\r\n\r\n    @GetMapping(\"/sendSm\")\r\n    public Result sendSm(@RequestParam(\"mobile\") String mobile, @RequestParam(\"code\") String code, User user) {\r\n        smsApi.send(mobile, code, user.getId(), \"1\", \"\");\r\n        return Result.ok();\r\n    }\r\n    \r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/zy/app/ctrl/AppCTRL.java b/src/main/java/com/zy/app/ctrl/AppCTRL.java
--- a/src/main/java/com/zy/app/ctrl/AppCTRL.java	(revision 675c1b730e42e2e479759d13ea62c9a740e178fb)
+++ b/src/main/java/com/zy/app/ctrl/AppCTRL.java	(date 1753321308925)
@@ -32,7 +32,9 @@
 import java.io.FileInputStream;
 import java.io.IOException;
 import java.util.ArrayList;
+import java.util.HashMap;
 import java.util.List;
+import java.util.Map;
 import java.util.concurrent.TimeUnit;
 
 @Tag(name = "系统全局接口")
@@ -90,6 +92,14 @@
         return Result.ok();
     }
 
+    @Operation(summary = "获取系统配置信息")
+    @GetMapping("/config")
+    public DataResult<Map<String, Object>> getConfig() {
+        Map<String, Object> config = new HashMap<>();
+        config.put("superAdminAccount", svc.getSuperAdminAccount());
+        return DataResult.data(config);
+    }
+
     @Operation(summary = "验证码")
     @GetMapping("/verifyCode/{uid}")
     public void verifyCode(@Parameter(description = "唯一标识") @PathVariable("uid") String uid, @Parameter(description = "位数") Integer digit, @Parameter(description = "类型 : 或逻辑，1-数字，2-大写字母；4-小写字母") Integer type, @Parameter(hidden = true) HttpServletResponse response) {
Index: src/main/java/com/zy/app/svc/AppSVC.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.zy.app.svc;\r\n\r\nimport com.zy.app.dao.AppDAO;\r\nimport com.zy.app.vo.Menu;\r\nimport com.zy.app.vo.User;\r\nimport com.zy.core.EX;\r\nimport com.zy.core.IApp;\r\nimport com.zy.dam.asset.dao.AmAssetDAO;\r\nimport com.zy.dam.feign.AmapGisFeign;\r\nimport com.zy.model.BaseUser;\r\nimport com.zy.model.Result;\r\nimport com.zy.model.TreeNode;\r\nimport com.zy.model.TypeKeyValue;\r\nimport com.zy.sys.dao.SysAccessDAO;\r\nimport com.zy.util.CryptUtils;\r\nimport org.springframework.beans.factory.annotation.Value;\r\nimport org.springframework.stereotype.Service;\r\n\r\nimport javax.annotation.Resource;\r\nimport java.util.*;\r\n\r\n@Service\r\npublic class AppSVC implements IApp {\r\n\r\n    @Resource\r\n    private AppDAO dao;\r\n\r\n    @Resource\r\n    private SysAccessDAO accessDAO;\r\n\r\n    @Resource\r\n    private AmapGisFeign amapGisFeign;\r\n\r\n    @Resource\r\n    private AmAssetDAO amAssetDAO;\r\n\r\n    @Value(\"${zy.super-admin-name}\")\r\n    private String superAdminAccount;\r\n\r\n    @Override\r\n    public Class<? extends BaseUser> getUserClass() {\r\n        return User.class;\r\n    }\r\n\r\n    @Override\r\n    public boolean checkAuth(BaseUser user, String api) {\r\n        return false;\r\n    }\r\n\r\n    @Override\r\n    public boolean beforeAccess(BaseUser user, String uri, String method, String ip, String uuid) {\r\n        if (uri.indexOf(\"heartbeat\") != -1 || uri.toLowerCase().indexOf(\"upload\") != -1) return true;\r\n        accessDAO.insert(uri, ip, user.getId(), \"\");\r\n        return false;\r\n    }\r\n\r\n    @Override\r\n    public boolean afterAccess(String uuid) {\r\n        return false;\r\n    }\r\n\r\n    public void loginSuccess(User user) {\r\n\r\n    }\r\n\r\n    /**\r\n     * 获取登录用户\r\n     *\r\n     * @param account  帐号\r\n     * @param password 密码\r\n     * @return 用户信息\r\n     */\r\n    public User findForLogin(String account, String password) {\r\n        if (account == null || password == null) return null;\r\n        User user = dao.findLogin(account, CryptUtils.md5(password));\r\n        if (user == null) return null;\r\n        if (!\"1\".equals(user.getStatus())) {\r\n            throw new EX(\"帐号已经被禁用\");\r\n        }\r\n        // 加载下级机构ID列表\r\n        user.setDeptIds(TreeNode.toPlainIdByList(dao.findDeptIdTree(), user.getDept(), true, null));\r\n        return user;\r\n    }\r\n\r\n    /**\r\n     * 附加信息\r\n     *\r\n     * @param user 用户\r\n     * @return User 用户信息\r\n     */\r\n    public User append(User user) {\r\n        // 加载用户菜单\r\n        loadMenu(user);\r\n        // 加载权限按钮\r\n        user.setOpts(dao.findOpt(user.getId()));\r\n        //加载字典\r\n        user.setDictCodes(findAllCode());\r\n        return user;\r\n    }\r\n\r\n    /**\r\n     * 更新密码\r\n     *\r\n     * @param id          ID\r\n     * @param oldPassword 旧密码\r\n     * @param newPassword 新密码\r\n     * @return 结果\r\n     */\r\n    public Result updatePassword(String id, String oldPassword, String newPassword) {\r\n        String password = dao.findPassword(id);\r\n        if (password == null) return Result.err(\"无法获取用户信息\");\r\n        if (!CryptUtils.verifyMd5(password, oldPassword)) return Result.err(\"原密码不正确\");\r\n        dao.updatePassword(id, CryptUtils.md5(newPassword));\r\n        return Result.ok();\r\n    }\r\n\r\n    /**\r\n     * 加载用户菜单\r\n     *\r\n     * @param user 用户\r\n     */\r\n    public void loadMenu(User user) {\r\n        if (user == null || user.getId() == null) return;\r\n        List<Menu> menus = superAdminAccount.equals(user.getAccount()) ? dao.findAllMenu() : dao.findUserMenu(user.getId());\r\n        List<Menu> roots = new ArrayList<>();\r\n        Map<String, Menu> map = new HashMap<>();\r\n        for (Menu menu : menus) { //遍历并初始化根级\r\n            map.put(menu.getCode(), menu);\r\n            if (\"0\".equals(menu.getPcode()) || \"00\".equals(menu.getPcode())) {\r\n                roots.add(menu);\r\n            }\r\n        }\r\n        Menu cm;\r\n        for (Menu menu : menus) { //遍历调整上下级关系\r\n            if (\"0\".equals(menu.getPcode()) || \"00\".equals(menu.getPcode())) continue;\r\n            cm = map.get(menu.getPcode());\r\n            if (cm != null) cm.addChild(menu);\r\n        }\r\n        //移除空目录\r\n        removeEmptyNode(roots);\r\n        user.setMenus(roots);\r\n    }\r\n\r\n    /**\r\n     * 移除空目录\r\n     *\r\n     * @param nodes 处理节点\r\n     */\r\n    private void removeEmptyNode(List<Menu> nodes) {\r\n        if (nodes == null || nodes.isEmpty()) return;\r\n        Menu node;\r\n        Iterator<Menu> it = nodes.iterator();\r\n        while (it.hasNext()) {\r\n            node = it.next();\r\n            removeEmptyNode(node.getChildren()); //递归算法\r\n            if (node.isEmpty() && (node.getPath() == null || node.getPath().isEmpty() || \"1\".equals(node.getType()))) { //没有子节点，并且不是菜单项则移除\r\n                it.remove();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取所有字典编码\r\n     *\r\n     * @return List\r\n     */\r\n    public List<TypeKeyValue> findAllCode() {\r\n        return dao.findAllCode();\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/zy/app/svc/AppSVC.java b/src/main/java/com/zy/app/svc/AppSVC.java
--- a/src/main/java/com/zy/app/svc/AppSVC.java	(revision 675c1b730e42e2e479759d13ea62c9a740e178fb)
+++ b/src/main/java/com/zy/app/svc/AppSVC.java	(date *************)
@@ -168,4 +168,12 @@
         return dao.findAllCode();
     }
 
+    /**
+     * 获取超级管理员账号名
+     * @return 超级管理员账号名
+     */
+    public String getSuperAdminAccount() {
+        return superAdminAccount;
+    }
+
 }
