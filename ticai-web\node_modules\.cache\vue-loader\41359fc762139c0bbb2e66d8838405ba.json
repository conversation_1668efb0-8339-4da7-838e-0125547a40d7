{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\sys\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\sys\\user\\index.vue", "mtime": 1753320380499}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUGFnZVRhYmxlIGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9QYWdlVGFibGUudnVlJw0KaW1wb3J0IFRyZWVCb3ggZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1RyZWVCb3gudnVlJw0KaW1wb3J0IFJvbGUgZnJvbSAnLi9yb2xlLnZ1ZScNCmNvbnN0IHN0YXR1cyA9IHsgJzEnOiAn5Zyo6IGMJywgJzUnOiAn56a76IGMJywgJzgnOiAn56aB55SoJyB9DQpjb25zdCB0eXBlID0geyAnMSc6ICflhoXpg6jnlKjmiLcnLCAnMic6ICflpJbpg6jnlKjmiLcnIH0NCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IFBhZ2VUYWJsZSwgVHJlZUJveCwgUm9sZSB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBkZXRhaWxWaXNpYmxlOiBmYWxzZSwNCiAgICAgIG9wdDogbnVsbCwgLy8g5pON5L2c57G75Z6LDQogICAgICBxZm9ybTogeyBhY2NvdW50OiBudWxsLCBuYW1lOiBudWxsLCBkZXB0OiBudWxsIH0sDQogICAgICAvLyDmnLrmnoTmlbDmja7liJfooagNCiAgICAgIGRlcHRUcmVlOiBbXSwNCiAgICAgIGZvcm06IHsgZ2VuZGVyOiAnMScgfSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGRlcHQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6YOo6ZeoJyB9XSwNCiAgICAgICAgYWNjb3VudDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXluJDlj7cnLCB0cmlnZ2VyOiAnYmx1cicgfV0sDQogICAgICAgIG5hbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5aeT5ZCNJywgdHJpZ2dlcjogJ2JsdXInIH1dLA0KICAgICAgICB0eXBlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeeUqOaIt+exu+WeiycsIHRyaWdnZXI6ICdibHVyJyB9XQ0KICAgICAgfSwNCiAgICAgIHBhc3N3b3JkVmlzaWJsZTogZmFsc2UsDQogICAgICBwYXNzd29yZGZvcm06IHt9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMubG9hZERlcHRUcmVlKCkNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLnNlYXJjaCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBpc1N1cGVyQWRtaW5BY2NvdW50KGFjY291bnQpIHsNCiAgICAgIC8vIOWIpOaWreaYr+WQpuS4uui2hee6p+euoeeQhuWRmOi0puWPt++8jOS7jumFjee9ruS4reiOt+WPlui2hee6p+euoeeQhuWRmOi0puWPt+WQjQ0KICAgICAgLy8g6L+Z6YeM5L2/55So6YWN572u55qE6LaF57qn566h55CG5ZGY6LSm5Y+35ZCN6L+b6KGM5Yik5patDQogICAgICByZXR1cm4gYWNjb3VudCA9PT0gJ2hudGljYWknIC8vIOi/memHjOW6lOivpeS7juWQjuerr+mFjee9ruiOt+WPlu+8jOaaguaXtuehrOe8lueggQ0KICAgIH0sDQogICAgbG9hZERlcHRUcmVlKCkgew0KICAgICAgdGhpcy4kaHR0cCgnL3N5cy9kZXB0L3RyZWVCeVR5cGUvMScpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5kZXB0VHJlZSA9IHJlcw0KICAgICAgfSkuY2F0Y2goKCkgPT4geyB0aGlzLiRhbGVydCgn5Yqg6L295py65p6E5qCR5Ye66ZSZJykgfSkNCiAgICB9LA0KICAgIGZuU3RhdHVzKHIsIGMsIHYsIGkpIHsNCiAgICAgIHJldHVybiBzdGF0dXNbdl0gfHwgJycNCiAgICB9LA0KICAgIGZuVHlwZShyLCBjLCB2LCBpKSB7DQogICAgICByZXR1cm4gdHlwZVt2XSB8fCAnJw0KICAgIH0sDQogICAgc2VhcmNoKCkgew0KICAgICAgaWYgKHRoaXMucWZvcm0uZGVwdCA9PT0gJzAnKSB0aGlzLnFmb3JtLmRlcHQgPSBudWxsDQogICAgICB0aGlzLiRyZWZzLmdyaWQuc2VhcmNoKHRoaXMucWZvcm0pDQogICAgfSwNCiAgICBjbGVhclZhbGlkYXRlKCkgew0KICAgICAgaWYgKHRoaXMuJHJlZnMuZGF0YWZvcm0pIHsNCiAgICAgICAgdGhpcy4kcmVmcy5kYXRhZm9ybS5jbGVhclZhbGlkYXRlKCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRyZWZzLmRhdGFmb3JtLmNsZWFyVmFsaWRhdGUoKQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgYWRkKCkgew0KICAgICAgdGhpcy5vcHQgPSAnYWRkJw0KICAgICAgdGhpcy5kZXRhaWxWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy5jbGVhclZhbGlkYXRlKCkNCiAgICAgIHRoaXMuZm9ybSA9IHsgZ2VuZGVyOiAnMScgfQ0KICAgIH0sDQogICAgZGV0YWlsKGRhdGEpIHsNCiAgICAgIHRoaXMub3B0ID0gJ3VwZGF0ZScNCiAgICAgIHRoaXMuZGV0YWlsVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuY2xlYXJWYWxpZGF0ZSgpDQogICAgICB0aGlzLmZvcm0gPSBPYmplY3QuYXNzaWduKHt9LCBkYXRhKQ0KICAgIH0sDQogICAgYXNzaWduUm9sZShkYXRhKSB7DQogICAgICB0aGlzLiRyZWZzLnJvbGUuc2hvdyhkYXRhLmlkKQ0KICAgIH0sDQogICAgcmVtb3ZlKGRhdGEpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeeUqOaItywg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7IGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsIHR5cGU6ICd3YXJuaW5nJyB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kaHR0cCh7IHVybDogJy9zeXMvdXNlci9kZWxldGUvJyArIGRhdGEuaWQgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICAgICAgICAgIHRoaXMuZGV0YWlsVmlzaWJsZSA9IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuc2VhcmNoKCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIHJlc2V0UGFzc3dvcmQoZGF0YSkgew0KICAgICAgdGhpcy5wYXNzd29yZGZvcm0gPSBkYXRhDQogICAgICB0aGlzLnBhc3N3b3JkVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIHNhdmVQYXNzd29yZCgpIHsNCiAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvc3lzL3VzZXIvcmVzZXRQYXNzd29yZCcsIGRhdGE6IHsgdmFsdWU6IHRoaXMucGFzc3dvcmRmb3JtLmlkLCB0ZXh0OiB0aGlzLnBhc3N3b3JkZm9ybS5wYXNzd29yZCB9fSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPiAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfph43nva7miJDlip8nKQ0KICAgICAgICAgIHRoaXMucGFzc3dvcmRWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCiAgICBzYXZlKCkgew0KICAgICAgdGhpcy4kcmVmcy5kYXRhZm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICghdGhpcy5mb3JtLnR5cGUpIHRoaXMuZm9ybS50eXBlID0gJzEnDQogICAgICAgICAgdGhpcy4kaHR0cCh7IHVybDogJy9zeXMvdXNlci8nICsgdGhpcy5vcHQsIGRhdGE6IHRoaXMuZm9ybSB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPiAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5o+Q5Lqk5oiQ5YqfJykNCiAgICAgICAgICAgICAgdGhpcy5kZXRhaWxWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5zZWFyY2goKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/sys/user", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"filter\">\r\n      <el-form :model=\"qform\" inline @submit.native.prevent=\"search\">\r\n        <div class=\"filter-item\">\r\n          <label>帐号：</label>\r\n          <div>\r\n            <el-input v-model=\"qform.account\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <label>姓名：</label>\r\n          <div>\r\n            <el-input v-model=\"qform.name\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <label>所属单位：</label>\r\n          <div>\r\n            <tree-box v-model=\"qform.dept\" :data=\"deptTree\" expand-all clearable />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search\">查询</el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-plus\" @click=\"add\">新增</el-button>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n    <page-table ref=\"grid\" v-table-height path=\"/sys/user/page\" size=\"mini\" stripe border>\r\n      <el-table-column type=\"index\" width=\"50\" />\r\n      <el-table-column label=\"帐号\" prop=\"account\" width=\"120\" />\r\n      <el-table-column label=\"姓名\" prop=\"name\" width=\"150\" />\r\n      <el-table-column label=\"性别\" prop=\"gender\" width=\"50\" align=\"center\" :formatter=\"colGender\" />\r\n      <el-table-column label=\"状态\" prop=\"status\" width=\"60\" align=\"center\" :formatter=\"fnStatus\" />\r\n      <el-table-column label=\"所属机构\" prop=\"deptName\" width=\"100\" />\r\n      <el-table-column label=\"用户类型\" prop=\"type\" width=\"70\" align=\"center\" :formatter=\"fnType\" />\r\n      <el-table-column label=\"角色\" prop=\"roleNames\" />\r\n      <el-table-column label=\"操作\" width=\"250\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"!isSuperAdminAccount(scope.row.account)\">\r\n            <el-button type=\"primary\" size=\"mini\" @click.stop=\"detail(scope.row)\">编辑</el-button>\r\n            <el-button type=\"success\" size=\"mini\" @click.stop=\"assignRole(scope.row)\">角色</el-button>\r\n            <el-button type=\"danger\" size=\"mini\" @click.stop=\"remove(scope.row)\">删除</el-button>\r\n            <el-button type=\"warning\" size=\"mini\" @click.stop=\"resetPassword(scope.row)\">重置密码</el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </page-table>\r\n    <el-dialog v-dialog-drag title=\"用户信息\" :visible.sync=\"detailVisible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"dataform\" :model=\"form\" :rules=\"rules\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"所属机构：\" prop=\"dept\">\r\n              <el-input v-if=\"/2|3/.test(form.type)\" v-model=\"form.deptName\" readonly class=\"readonly\" />\r\n              <tree-box v-else v-model=\"form.dept\" :data=\"deptTree\" expand-all :clearable=\"false\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"姓名：\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性别：\" prop=\"gender\">\r\n              <el-radio-group v-model=\"form.gender\">\r\n                <el-radio label=\"1\">男</el-radio>\r\n                <el-radio label=\"2\">女</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"帐号：\" prop=\"account\">\r\n              <el-input v-model=\"form.account\" :readonly=\"opt == 'update'\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <span class=\"form-memo\">{{ opt == 'add' ? '初始化密码为：Hntc@1234' : '帐号不能修改' }}</span>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"编码：\" prop=\"no\">\r\n              <el-input v-model=\"form.no\" maxlength=\"12\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n              <el-input v-model=\"form.phone\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <role ref=\"role\" @success=\"search\" />\r\n    <el-dialog v-dialog-drag title=\"重置密码\" :visible.sync=\"passwordVisible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"passwordform\" :model=\"passwordform\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"姓名：\" prop=\"name\">\r\n              <el-input v-model=\"passwordform.name\" readonly class=\"readonly\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"帐号：\" prop=\"account\">\r\n              <el-input v-model=\"passwordform.account\" readonly class=\"readonly\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"重置密码：\" prop=\"password\">\r\n              <el-input v-model=\"passwordform.password\" maxlength=\"64\" autocomplete=\"off\" placeholder=\"不填表示重置为Hntc@1234\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <span class=\"form-memo\">密码包含：大写字母、小写字母、数字和特殊符号,长度8~20</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"passwordVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"savePassword\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport Role from './role.vue'\r\nconst status = { '1': '在职', '5': '离职', '8': '禁用' }\r\nconst type = { '1': '内部用户', '2': '外部用户' }\r\n\r\nexport default {\r\n  components: { PageTable, TreeBox, Role },\r\n  data() {\r\n    return {\r\n      detailVisible: false,\r\n      opt: null, // 操作类型\r\n      qform: { account: null, name: null, dept: null },\r\n      // 机构数据列表\r\n      deptTree: [],\r\n      form: { gender: '1' },\r\n      rules: {\r\n        dept: [{ required: true, message: '请选择部门' }],\r\n        account: [{ required: true, message: '请输入帐号', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\r\n        type: [{ required: true, message: '请输入用户类型', trigger: 'blur' }]\r\n      },\r\n      passwordVisible: false,\r\n      passwordform: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.loadDeptTree()\r\n  },\r\n  mounted() {\r\n    this.search()\r\n  },\r\n  methods: {\r\n    isSuperAdminAccount(account) {\r\n      // 判断是否为超级管理员账号，从配置中获取超级管理员账号名\r\n      // 这里使用配置的超级管理员账号名进行判断\r\n      return account === 'hnticai' // 这里应该从后端配置获取，暂时硬编码\r\n    },\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    fnStatus(r, c, v, i) {\r\n      return status[v] || ''\r\n    },\r\n    fnType(r, c, v, i) {\r\n      return type[v] || ''\r\n    },\r\n    search() {\r\n      if (this.qform.dept === '0') this.qform.dept = null\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    clearValidate() {\r\n      if (this.$refs.dataform) {\r\n        this.$refs.dataform.clearValidate()\r\n      } else {\r\n        this.$nextTick(() => {\r\n          this.$refs.dataform.clearValidate()\r\n        })\r\n      }\r\n    },\r\n    add() {\r\n      this.opt = 'add'\r\n      this.detailVisible = true\r\n      this.clearValidate()\r\n      this.form = { gender: '1' }\r\n    },\r\n    detail(data) {\r\n      this.opt = 'update'\r\n      this.detailVisible = true\r\n      this.clearValidate()\r\n      this.form = Object.assign({}, data)\r\n    },\r\n    assignRole(data) {\r\n      this.$refs.role.show(data.id)\r\n    },\r\n    remove(data) {\r\n      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })\r\n        .then(() => {\r\n          this.$http({ url: '/sys/user/delete/' + data.id }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('删除成功')\r\n              this.detailVisible = false\r\n              this.search()\r\n            }\r\n          })\r\n        }).catch(() => {})\r\n    },\r\n    resetPassword(data) {\r\n      this.passwordform = data\r\n      this.passwordVisible = true\r\n    },\r\n    savePassword() {\r\n      this.$http({ url: '/sys/user/resetPassword', data: { value: this.passwordform.id, text: this.passwordform.password }}).then(res => {\r\n        if (res.code > 0) {\r\n          this.$message.success('重置成功')\r\n          this.passwordVisible = false\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    save() {\r\n      this.$refs.dataform.validate(valid => {\r\n        if (valid) {\r\n          if (!this.form.type) this.form.type = '1'\r\n          this.$http({ url: '/sys/user/' + this.opt, data: this.form }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('提交成功')\r\n              this.detailVisible = false\r\n              this.search()\r\n            }\r\n          }).catch(() => {})\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}