{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\sys\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\sys\\user\\index.vue", "mtime": 1753321388789}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUGFnZVRhYmxlIGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9QYWdlVGFibGUudnVlJw0KaW1wb3J0IFRyZWVCb3ggZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1RyZWVCb3gudnVlJw0KaW1wb3J0IFJvbGUgZnJvbSAnLi9yb2xlLnZ1ZScNCmNvbnN0IHN0YXR1cyA9IHsgJzEnOiAn5Zyo6IGMJywgJzUnOiAn56a76IGMJywgJzgnOiAn56aB55SoJyB9DQpjb25zdCB0eXBlID0geyAnMSc6ICflhoXpg6jnlKjmiLcnLCAnMic6ICflpJbpg6jnlKjmiLcnIH0NCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IFBhZ2VUYWJsZSwgVHJlZUJveCwgUm9sZSB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBkZXRhaWxWaXNpYmxlOiBmYWxzZSwNCiAgICAgIG9wdDogbnVsbCwgLy8g5pON5L2c57G75Z6LDQogICAgICBxZm9ybTogeyBhY2NvdW50OiBudWxsLCBuYW1lOiBudWxsLCBkZXB0OiBudWxsIH0sDQogICAgICAvLyDmnLrmnoTmlbDmja7liJfooagNCiAgICAgIGRlcHRUcmVlOiBbXSwNCiAgICAgIGZvcm06IHsgZ2VuZGVyOiAnMScgfSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGRlcHQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6YOo6ZeoJyB9XSwNCiAgICAgICAgYWNjb3VudDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXluJDlj7cnLCB0cmlnZ2VyOiAnYmx1cicgfV0sDQogICAgICAgIG5hbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5aeT5ZCNJywgdHJpZ2dlcjogJ2JsdXInIH1dLA0KICAgICAgICB0eXBlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeeUqOaIt+exu+WeiycsIHRyaWdnZXI6ICdibHVyJyB9XQ0KICAgICAgfSwNCiAgICAgIHBhc3N3b3JkVmlzaWJsZTogZmFsc2UsDQogICAgICBwYXNzd29yZGZvcm06IHt9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMubG9hZERlcHRUcmVlKCkNCiAgICAvLyDliqDovb3ns7vnu5/phY3nva4NCiAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndXNlci9sb2FkU3lzdGVtQ29uZmlnJykuY2F0Y2goKCkgPT4gew0KICAgICAgY29uc29sZS53YXJuKCfojrflj5bns7vnu5/phY3nva7lpLHotKUnKQ0KICAgIH0pDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5zZWFyY2goKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaXNTdXBlckFkbWluQWNjb3VudCh1c2VyUm93KSB7DQogICAgICAvLyDliKTmlq3mmK/lkKbkuLrotoXnuqfnrqHnkIblkZjotKblj7cNCiAgICAgIC8vIOaWueahiDHvvJrlpoLmnpzlkI7nq6/ov5Tlm57nmoTnlKjmiLfliJfooajkuK3ljIXlkKtpc1N1cGVyQWRtaW7lrZfmrrXvvIznm7TmjqXkvb/nlKgNCiAgICAgIGlmICh1c2VyUm93LmlzU3VwZXJBZG1pbiAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgIHJldHVybiB1c2VyUm93LmlzU3VwZXJBZG1pbg0KICAgICAgfQ0KDQogICAgICAvLyDmlrnmoYgy77ya6YCa6L+H5LuO5ZCO56uv6I635Y+W55qE6LaF57qn566h55CG5ZGY6LSm5Y+35ZCN6L+b6KGM5q+U6L6DDQogICAgICBjb25zdCBzeXN0ZW1Db25maWcgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnN5c3RlbUNvbmZpZw0KICAgICAgaWYgKHN5c3RlbUNvbmZpZyAmJiBzeXN0ZW1Db25maWcuc3VwZXJBZG1pbkFjY291bnQpIHsNCiAgICAgICAgcmV0dXJuIHN5c3RlbUNvbmZpZy5zdXBlckFkbWluQWNjb3VudCA9PT0gdXNlclJvdy5hY2NvdW50DQogICAgICB9DQoNCiAgICAgIC8vIOaWueahiDPvvJrpgJrov4flvZPliY3nmbvlvZXnlKjmiLfnmoTotKblj7fov5vooYzmr5TovoPvvIjlpIfnlKjmlrnmoYjvvIkNCiAgICAgIGNvbnN0IGN1cnJlbnRVc2VyID0gdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VyDQogICAgICBpZiAoY3VycmVudFVzZXIgJiYgY3VycmVudFVzZXIuaXNTdXBlckFkbWluKSB7DQogICAgICAgIHJldHVybiBjdXJyZW50VXNlci5hY2NvdW50ID09PSB1c2VyUm93LmFjY291bnQNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIGZhbHNlDQogICAgfSwNCiAgICBsb2FkU3lzdGVtQ29uZmlnKCkgew0KICAgICAgLy8g5LuO5ZCO56uv6I635Y+W57O757uf6YWN572u77yM5YyF5ous6LaF57qn566h55CG5ZGY6LSm5Y+35ZCNDQogICAgICB0aGlzLiRodHRwKCcvYXBwL2NvbmZpZycpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcyAmJiByZXMuc3VwZXJBZG1pbkFjY291bnQpIHsNCiAgICAgICAgICB0aGlzLnN1cGVyQWRtaW5BY2NvdW50ID0gcmVzLnN1cGVyQWRtaW5BY2NvdW50DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgY29uc29sZS53YXJuKCfojrflj5bns7vnu5/phY3nva7lpLHotKUnKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGxvYWREZXB0VHJlZSgpIHsNCiAgICAgIHRoaXMuJGh0dHAoJy9zeXMvZGVwdC90cmVlQnlUeXBlLzEnKS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuZGVwdFRyZWUgPSByZXMNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsgdGhpcy4kYWxlcnQoJ+WKoOi9veacuuaehOagkeWHuumUmScpIH0pDQogICAgfSwNCiAgICBmblN0YXR1cyhyLCBjLCB2LCBpKSB7DQogICAgICByZXR1cm4gc3RhdHVzW3ZdIHx8ICcnDQogICAgfSwNCiAgICBmblR5cGUociwgYywgdiwgaSkgew0KICAgICAgcmV0dXJuIHR5cGVbdl0gfHwgJycNCiAgICB9LA0KICAgIHNlYXJjaCgpIHsNCiAgICAgIGlmICh0aGlzLnFmb3JtLmRlcHQgPT09ICcwJykgdGhpcy5xZm9ybS5kZXB0ID0gbnVsbA0KICAgICAgdGhpcy4kcmVmcy5ncmlkLnNlYXJjaCh0aGlzLnFmb3JtKQ0KICAgIH0sDQogICAgY2xlYXJWYWxpZGF0ZSgpIHsNCiAgICAgIGlmICh0aGlzLiRyZWZzLmRhdGFmb3JtKSB7DQogICAgICAgIHRoaXMuJHJlZnMuZGF0YWZvcm0uY2xlYXJWYWxpZGF0ZSgpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy4kcmVmcy5kYXRhZm9ybS5jbGVhclZhbGlkYXRlKCkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGFkZCgpIHsNCiAgICAgIHRoaXMub3B0ID0gJ2FkZCcNCiAgICAgIHRoaXMuZGV0YWlsVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuY2xlYXJWYWxpZGF0ZSgpDQogICAgICB0aGlzLmZvcm0gPSB7IGdlbmRlcjogJzEnIH0NCiAgICB9LA0KICAgIGRldGFpbChkYXRhKSB7DQogICAgICB0aGlzLm9wdCA9ICd1cGRhdGUnDQogICAgICB0aGlzLmRldGFpbFZpc2libGUgPSB0cnVlDQogICAgICB0aGlzLmNsZWFyVmFsaWRhdGUoKQ0KICAgICAgdGhpcy5mb3JtID0gT2JqZWN0LmFzc2lnbih7fSwgZGF0YSkNCiAgICB9LA0KICAgIGFzc2lnblJvbGUoZGF0YSkgew0KICAgICAgdGhpcy4kcmVmcy5yb2xlLnNob3coZGF0YS5pZCkNCiAgICB9LA0KICAgIHJlbW92ZShkYXRhKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XnlKjmiLcsIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgeyBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLCB0eXBlOiAnd2FybmluZycgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvc3lzL3VzZXIvZGVsZXRlLycgKyBkYXRhLmlkIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA+IDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgICB0aGlzLmRldGFpbFZpc2libGUgPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLnNlYXJjaCgpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCiAgICByZXNldFBhc3N3b3JkKGRhdGEpIHsNCiAgICAgIHRoaXMucGFzc3dvcmRmb3JtID0gZGF0YQ0KICAgICAgdGhpcy5wYXNzd29yZFZpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICBzYXZlUGFzc3dvcmQoKSB7DQogICAgICB0aGlzLiRodHRwKHsgdXJsOiAnL3N5cy91c2VyL3Jlc2V0UGFzc3dvcmQnLCBkYXRhOiB7IHZhbHVlOiB0aGlzLnBhc3N3b3JkZm9ybS5pZCwgdGV4dDogdGhpcy5wYXNzd29yZGZvcm0ucGFzc3dvcmQgfX0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6YeN572u5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLnBhc3N3b3JkVmlzaWJsZSA9IGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQ0KICAgIH0sDQogICAgc2F2ZSgpIHsNCiAgICAgIHRoaXMuJHJlZnMuZGF0YWZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAoIXRoaXMuZm9ybS50eXBlKSB0aGlzLmZvcm0udHlwZSA9ICcxJw0KICAgICAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvc3lzL3VzZXIvJyArIHRoaXMub3B0LCBkYXRhOiB0aGlzLmZvcm0gfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aPkOS6pOaIkOWKnycpDQogICAgICAgICAgICAgIHRoaXMuZGV0YWlsVmlzaWJsZSA9IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuc2VhcmNoKCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/sys/user", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"filter\">\r\n      <el-form :model=\"qform\" inline @submit.native.prevent=\"search\">\r\n        <div class=\"filter-item\">\r\n          <label>帐号：</label>\r\n          <div>\r\n            <el-input v-model=\"qform.account\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <label>姓名：</label>\r\n          <div>\r\n            <el-input v-model=\"qform.name\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <label>所属单位：</label>\r\n          <div>\r\n            <tree-box v-model=\"qform.dept\" :data=\"deptTree\" expand-all clearable />\r\n          </div>\r\n        </div>\r\n        <div class=\"filter-item\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search\">查询</el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-plus\" @click=\"add\">新增</el-button>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n    <page-table ref=\"grid\" v-table-height path=\"/sys/user/page\" size=\"mini\" stripe border>\r\n      <el-table-column type=\"index\" width=\"50\" />\r\n      <el-table-column label=\"帐号\" prop=\"account\" width=\"120\" />\r\n      <el-table-column label=\"姓名\" prop=\"name\" width=\"150\" />\r\n      <el-table-column label=\"性别\" prop=\"gender\" width=\"50\" align=\"center\" :formatter=\"colGender\" />\r\n      <el-table-column label=\"状态\" prop=\"status\" width=\"60\" align=\"center\" :formatter=\"fnStatus\" />\r\n      <el-table-column label=\"所属机构\" prop=\"deptName\" width=\"100\" />\r\n      <el-table-column label=\"用户类型\" prop=\"type\" width=\"70\" align=\"center\" :formatter=\"fnType\" />\r\n      <el-table-column label=\"角色\" prop=\"roleNames\" />\r\n      <el-table-column label=\"操作\" width=\"250\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"!isSuperAdminAccount(scope.row)\">\r\n            <el-button type=\"primary\" size=\"mini\" @click.stop=\"detail(scope.row)\">编辑</el-button>\r\n            <el-button type=\"success\" size=\"mini\" @click.stop=\"assignRole(scope.row)\">角色</el-button>\r\n            <el-button type=\"danger\" size=\"mini\" @click.stop=\"remove(scope.row)\">删除</el-button>\r\n            <el-button type=\"warning\" size=\"mini\" @click.stop=\"resetPassword(scope.row)\">重置密码</el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </page-table>\r\n    <el-dialog v-dialog-drag title=\"用户信息\" :visible.sync=\"detailVisible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"dataform\" :model=\"form\" :rules=\"rules\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"所属机构：\" prop=\"dept\">\r\n              <el-input v-if=\"/2|3/.test(form.type)\" v-model=\"form.deptName\" readonly class=\"readonly\" />\r\n              <tree-box v-else v-model=\"form.dept\" :data=\"deptTree\" expand-all :clearable=\"false\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"姓名：\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性别：\" prop=\"gender\">\r\n              <el-radio-group v-model=\"form.gender\">\r\n                <el-radio label=\"1\">男</el-radio>\r\n                <el-radio label=\"2\">女</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"帐号：\" prop=\"account\">\r\n              <el-input v-model=\"form.account\" :readonly=\"opt == 'update'\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <span class=\"form-memo\">{{ opt == 'add' ? '初始化密码为：Hntc@1234' : '帐号不能修改' }}</span>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"编码：\" prop=\"no\">\r\n              <el-input v-model=\"form.no\" maxlength=\"12\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n              <el-input v-model=\"form.phone\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <role ref=\"role\" @success=\"search\" />\r\n    <el-dialog v-dialog-drag title=\"重置密码\" :visible.sync=\"passwordVisible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"passwordform\" :model=\"passwordform\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"姓名：\" prop=\"name\">\r\n              <el-input v-model=\"passwordform.name\" readonly class=\"readonly\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"帐号：\" prop=\"account\">\r\n              <el-input v-model=\"passwordform.account\" readonly class=\"readonly\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"重置密码：\" prop=\"password\">\r\n              <el-input v-model=\"passwordform.password\" maxlength=\"64\" autocomplete=\"off\" placeholder=\"不填表示重置为Hntc@1234\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <span class=\"form-memo\">密码包含：大写字母、小写字母、数字和特殊符号,长度8~20</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"passwordVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"savePassword\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport Role from './role.vue'\r\nconst status = { '1': '在职', '5': '离职', '8': '禁用' }\r\nconst type = { '1': '内部用户', '2': '外部用户' }\r\n\r\nexport default {\r\n  components: { PageTable, TreeBox, Role },\r\n  data() {\r\n    return {\r\n      detailVisible: false,\r\n      opt: null, // 操作类型\r\n      qform: { account: null, name: null, dept: null },\r\n      // 机构数据列表\r\n      deptTree: [],\r\n      form: { gender: '1' },\r\n      rules: {\r\n        dept: [{ required: true, message: '请选择部门' }],\r\n        account: [{ required: true, message: '请输入帐号', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\r\n        type: [{ required: true, message: '请输入用户类型', trigger: 'blur' }]\r\n      },\r\n      passwordVisible: false,\r\n      passwordform: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.loadDeptTree()\r\n    // 加载系统配置\r\n    this.$store.dispatch('user/loadSystemConfig').catch(() => {\r\n      console.warn('获取系统配置失败')\r\n    })\r\n  },\r\n  mounted() {\r\n    this.search()\r\n  },\r\n  methods: {\r\n    isSuperAdminAccount(userRow) {\r\n      // 判断是否为超级管理员账号\r\n      // 方案1：如果后端返回的用户列表中包含isSuperAdmin字段，直接使用\r\n      if (userRow.isSuperAdmin !== undefined) {\r\n        return userRow.isSuperAdmin\r\n      }\r\n\r\n      // 方案2：通过从后端获取的超级管理员账号名进行比较\r\n      const systemConfig = this.$store.getters.systemConfig\r\n      if (systemConfig && systemConfig.superAdminAccount) {\r\n        return systemConfig.superAdminAccount === userRow.account\r\n      }\r\n\r\n      // 方案3：通过当前登录用户的账号进行比较（备用方案）\r\n      const currentUser = this.$store.getters.user\r\n      if (currentUser && currentUser.isSuperAdmin) {\r\n        return currentUser.account === userRow.account\r\n      }\r\n\r\n      return false\r\n    },\r\n    loadSystemConfig() {\r\n      // 从后端获取系统配置，包括超级管理员账号名\r\n      this.$http('/app/config').then(res => {\r\n        if (res && res.superAdminAccount) {\r\n          this.superAdminAccount = res.superAdminAccount\r\n        }\r\n      }).catch(() => {\r\n        console.warn('获取系统配置失败')\r\n      })\r\n    },\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    fnStatus(r, c, v, i) {\r\n      return status[v] || ''\r\n    },\r\n    fnType(r, c, v, i) {\r\n      return type[v] || ''\r\n    },\r\n    search() {\r\n      if (this.qform.dept === '0') this.qform.dept = null\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    clearValidate() {\r\n      if (this.$refs.dataform) {\r\n        this.$refs.dataform.clearValidate()\r\n      } else {\r\n        this.$nextTick(() => {\r\n          this.$refs.dataform.clearValidate()\r\n        })\r\n      }\r\n    },\r\n    add() {\r\n      this.opt = 'add'\r\n      this.detailVisible = true\r\n      this.clearValidate()\r\n      this.form = { gender: '1' }\r\n    },\r\n    detail(data) {\r\n      this.opt = 'update'\r\n      this.detailVisible = true\r\n      this.clearValidate()\r\n      this.form = Object.assign({}, data)\r\n    },\r\n    assignRole(data) {\r\n      this.$refs.role.show(data.id)\r\n    },\r\n    remove(data) {\r\n      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })\r\n        .then(() => {\r\n          this.$http({ url: '/sys/user/delete/' + data.id }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('删除成功')\r\n              this.detailVisible = false\r\n              this.search()\r\n            }\r\n          })\r\n        }).catch(() => {})\r\n    },\r\n    resetPassword(data) {\r\n      this.passwordform = data\r\n      this.passwordVisible = true\r\n    },\r\n    savePassword() {\r\n      this.$http({ url: '/sys/user/resetPassword', data: { value: this.passwordform.id, text: this.passwordform.password }}).then(res => {\r\n        if (res.code > 0) {\r\n          this.$message.success('重置成功')\r\n          this.passwordVisible = false\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    save() {\r\n      this.$refs.dataform.validate(valid => {\r\n        if (valid) {\r\n          if (!this.form.type) this.form.type = '1'\r\n          this.$http({ url: '/sys/user/' + this.opt, data: this.form }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('提交成功')\r\n              this.detailVisible = false\r\n              this.search()\r\n            }\r\n          }).catch(() => {})\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}