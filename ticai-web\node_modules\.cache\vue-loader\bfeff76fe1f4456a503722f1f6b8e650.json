{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue?vue&type=style&index=0&id=51f74d7a&lang=scss&scope=true", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue", "mtime": 1753320296360}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1747730939289}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1747730939045}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1747730939142}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1747730941929}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5sYXlvdXQtbHIgew0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA1MnB4KTsNCn0NCg0KLmxheW91dC1sciAubGVmdCB7DQogIG1pbi13aWR0aDogMjIwcHg7DQp9DQoNCi5sYXlvdXQtbHIgLmNlbnRlciB7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5sb2NhdGlvbi1oZWFkIHsNCiAgaGVpZ2h0OiA0MHB4Ow0KfQ0KDQoubG9jYXRpb24tdGl0bGUgew0KICBsaW5lLWhlaWdodDogMzJweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLmxvY2F0aW9uLXNlYXJjaCB7DQogIHdpZHRoOiAzMDBweDsNCiAgZmxvYXQ6IHJpZ2h0Ow0KfQ0KDQoudXBsb2FkLWJsb2NrIHsNCiAgcGFkZGluZzogMCA0cHg7DQogIHdpZHRoOiBjYWxjKDEwMHZ3IC0gMTJweCk7DQogIGhlaWdodDogY2FsYygxMDB2aCAtIDI1MHB4KTsNCiAgb3ZlcmZsb3c6IGF1dG87DQp9DQoNCi51cGxvYWQtYmxvY2sgdGFibGUgew0KICBib3JkZXI6IDFweCBzb2xpZCAjY2NjY2NjOw0KICBib3JkZXItY29sbGFwc2U6IGNvbGxhcHNlOw0KfQ0KDQoudXBsb2FkLWJsb2NrIHRhYmxlIHRoLA0KLnVwbG9hZC1ibG9jayB0YWJsZSB0ZCB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNjY2NjY2M7DQogIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7DQogIHBhZGRpbmc6IDRweCA2cHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbGluZS1oZWlnaHQ6IDE4cHg7DQp9DQoNCi51cGxvYWQtYmxvY2sgdGFibGUgdHIuZXJyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZWU5MjsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0pBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/bd/region", "sourcesContent": ["<template>\r\n  <div class=\"layout-lr\">\r\n    <div class=\"left\">\r\n      <div class=\"head\">区域列表</div>\r\n      <div class=\"body\">\r\n        <ul>\r\n          <li :class=\"{ act: activeItem == null || activeItem.id == null }\" @click=\"showRegion({})\">所有区域</li>\r\n          <li v-for=\"item in regionList\" :key=\"item.id\" :class=\"{ act: activeItem && activeItem.id == item.id }\"\r\n            @click=\"showRegion(item)\">{{ item.name }}</li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n    <div class=\"center\">\r\n      <el-descriptions title=\"区域信息\" :column=\"3\" border class=\"descr-3\">\r\n        <template slot=\"extra\">\r\n          <div class=\"button-bar\">\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addRegion\">新增区域</el-button>\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-upload\" @click=\"upload\">网点导入</el-button>\r\n            <template v-if=\"activeItem && activeItem.id\">\r\n              <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-edit\" @click=\"editRegion\">编辑区域</el-button>\r\n              <el-button type=\"danger\" size=\"mini\" icon=\"el-icon-remove\" @click=\"removeRegion\">删除区域</el-button>\r\n              <el-button type=\"success\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addLocation\">新增网点</el-button>\r\n              <el-dropdown @command=\"handleBatchCommand\" style=\"margin-left:10px\">\r\n                <el-button type=\"warning\" size=\"mini\">\r\n                  批量修改<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item command=\"export\">导出</el-dropdown-item>\r\n                  <el-dropdown-item command=\"import\">导入</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </template>\r\n          </div>\r\n        </template>\r\n        <template v-if=\"activeItem && activeItem.id\">\r\n          <el-descriptions-item label=\"所属部门\">{{ activeItem.deptName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"区域代码\">{{ activeItem.code }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"区域名称\">{{ activeItem.name }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"所在区划\">{{ activeItem.regionName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"负责人\">{{ activeItem.userName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"联系电话\">{{ activeItem.userPhone }}</el-descriptions-item>\r\n          <el-descriptions-item :span=\"3\" label=\"区域范围\">{{ activeItem.scope }}</el-descriptions-item>\r\n        </template>\r\n      </el-descriptions>\r\n      <el-divider></el-divider>\r\n      <div class=\"location-head\">\r\n        <span class=\"location-title\">区域网点</span>\r\n        <div class=\"location-search\">\r\n          <el-input v-model=\"qform.keyword\" clearable size=\"mini\" placeholder=\"输入关键字\" autocomplete=\"off\">\r\n            <template slot=\"prepend\">检索:</template>\r\n            <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchLocation\"></el-button>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <page-table ref=\"grid\" size=\"mini\" path=\"/am/location/page\" :query=\"qform\" stripe border>\r\n        <el-table-column v-if=\"activeItem == null || activeItem.id == null\" label=\"所属区域\" prop=\"regionName\" width=\"100\"\r\n          align=\"center\" />\r\n        <el-table-column label=\"网点编码\" prop=\"code\" width=\"110\" align=\"center\" />\r\n        <el-table-column label=\"网点名称\" prop=\"name\" width=\"300\" align=\"center\" />\r\n        <el-table-column label=\"负责人\" prop=\"contact\" width=\"300\" align=\"center\" />\r\n        <el-table-column label=\"联系方式\" prop=\"phone\" width=\"150\" align=\"center\" />\r\n        <el-table-column label=\"地址\" prop=\"address\" width=\"800\" align=\"center\" />\r\n        <el-table-column label=\"网点时间\" prop=\"createTime\" align=\"center\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.createTime ? formatDateTime(scope.row.createTime) : '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"{ row }\">\r\n            <div v-if=\"row.id != 'admin'\">\r\n              <el-button type=\"primary\" size=\"mini\" @click.stop=\"editLocation(row)\">编辑</el-button>\r\n              <el-button type=\"danger\" size=\"mini\" @click.stop=\"removeLocation(row)\">删除</el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </page-table>\r\n    </div>\r\n    <el-dialog v-dialog-drag title=\"区域信息\" width=\"800px\" :visible.sync=\"regionVisible\" :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form ref=\"regionform\" :model=\"regionData\" :rules=\"regionRules\" label-width=\"110px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域编码：\" prop=\"code\">\r\n              <el-input v-model=\"regionData.code\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称：\" prop=\"name\">\r\n              <el-input v-model=\"regionData.name\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所属机构：\" prop=\"dept\">\r\n              <tree-box v-model=\"regionData.dept\" :data=\"deptTree\" :expand-all=\"true\" :clearable=\"false\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所在区划：\" prop=\"region\">\r\n              <region v-model=\"regionData.region\" root=\"460000\" :start-level=\"1\" with-root any-node\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人：\" prop=\"user\">\r\n              <user-chosen v-model=\"regionData.user\" type=\"1\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域排序：\" prop=\"ord\">\r\n              <el-input-number v-model=\"regionData.ord\" autocomplete=\"off\" :min=\"1\" :max=\"999\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"区域范围：\" prop=\"scope\">\r\n              <el-input v-model=\"regionData.scope\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"regionVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveRegion\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog v-dialog-drag title=\"区域地点\" width=\"800px\" :visible.sync=\"locationVisible\" :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form ref=\"locationform\" :model=\"locationData\" :rules=\"locationRules\" label-width=\"110px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域编码：\">\r\n              <el-input v-model=\"activeItem.code\" readonly class=\"form-static\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称：\">\r\n              <el-input v-model=\"activeItem.name\" readonly class=\"form-static\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"网点编码：\" prop=\"code\">\r\n              <el-input v-model=\"locationData.code\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"网点名称：\" prop=\"name\">\r\n              <el-input v-model=\"locationData.name\" maxlength=\"64\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人：\" prop=\"contact\">\r\n              <el-input v-model=\"locationData.contact\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话：\" prop=\"phone\">\r\n              <el-input v-model=\"locationData.phone\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"网点地址：\" prop=\"address\">\r\n              <el-input v-model=\"locationData.address\" maxlength=\"128\" autocomplete=\"off\">\r\n                <template slot=\"append\">\r\n                  <el-button size=\"mini\" icon=\"el-icon-map-location\" @click=\"mapPin\">地图定位</el-button>\r\n                </template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"网点备注：\" prop=\"memo\">\r\n              <el-input v-model=\"locationData.memo\" maxlength=\"128\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <el-table ref=\"optionGrid\" :data=\"amLocationAsset\" size=\"mini\" :stripe=\"true\" :border=\"true\">\r\n        <el-table-column type=\"index\" width=\"50\" align=\"center\" />\r\n        <el-table-column label=\"销售终端编号\" prop=\"sn\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-input v-model=\"scope.row.sn\" size=\"mini\" autocomplete=\"off\" />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column width=\"70\" align=\"center\">\r\n          <template slot=\"header\">\r\n            <el-button type=\"success\" size=\"mini\" @click=\"addAsset\">新增</el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"danger\" @click.stop=\"removeAsset(scope.$index)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"locationVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveLocation\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!--------网点导入------->\r\n    <el-dialog ref=\"uploadDlg\" title=\"批量导入\" fullscreen class=\"dialog-full\" :visible.sync=\"uploadVisible\"\r\n      :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <div style=\"margin: 10px 20px;\">\r\n        <upload-file v-model=\"fileList\" type=\"ASSET_UPLOAD\" simple :multiple=\"false\" :limit=\"1\" accept=\".xls,.xlsx\"\r\n          @success=\"uploaded\" @removeFile=\"uploadRemove\" />\r\n      </div>\r\n      <div class=\"upload-block\">\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th>行号</th>\r\n              <th>结果提示</th>\r\n              <th>所属市县编号</th>\r\n              <th>销售终端编号</th>\r\n              <th>门店编号</th>\r\n              <th>业主姓名</th>\r\n              <th>负责人</th>\r\n              <th>联系方式</th>\r\n              <th>门店地址</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <template v-for=\"item in uploadList\">\r\n              <tr v-if=\"showUploadAll || item.rowMsg != null\" :key=\"item.rowNum\" :class=\"{ err: item.rowMsg != null }\">\r\n                <td>{{ item.rowNum }}</td>\r\n                <td class=\"upload-msg\">{{ item.rowMsg }}</td>\r\n                <td>{{ item.region }}</td>\r\n                <td>{{ item.sn }}</td>\r\n                <td>{{ item.code }}</td>\r\n                <td>{{ item.name }}</td>\r\n                <td>{{ item.contact }}</td>\r\n                <td>{{ item.phone }}</td>\r\n                <td>{{ item.address }}</td>\r\n              </tr>\r\n            </template>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"left: 30px; float: left;\">\r\n          <el-button size=\"small\" type=\"warning\" @click=\"toggleErr\">{{ showUploadAll ? '查看错误' : '查看全部' }}</el-button>\r\n        </div>\r\n        <el-button size=\"small\" @click=\"uploadVisible = false\">取 消</el-button>\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitUpload\">确定上传</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog ref=\"batchUploadDlg\" title=\"网点批量修改\" fullscreen class=\"dialog-full\" :visible.sync=\"batchUploadVisible\"\r\n      :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <div style=\"margin: 10px 20px;\">\r\n        <upload-file v-model=\"batchFileList\" type=\"ASSET_UPLOAD\" simple :multiple=\"false\" :limit=\"1\" accept=\".xls,.xlsx\"\r\n          @success=\"batchUploaded\" @removeFile=\"batchUploadRemove\" />\r\n      </div>\r\n      <div class=\"upload-block\">\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th>行号</th>\r\n              <th v-if=\"getBatchErrorCount() > 0\">结果提示</th>\r\n              <th>网点编码</th>\r\n              <th>网点名称</th>\r\n              <th>网点备注</th>\r\n              <th>操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <template v-for=\"(item, index) in batchUploadList\">\r\n              <tr :key=\"index\" :class=\"{ err: item.rowMsg != null }\">\r\n                <td>{{ index + 1 }}</td>\r\n                <td v-if=\"getBatchErrorCount() > 0\" class=\"upload-msg\">{{ item.rowMsg }}</td>\r\n                <td>{{ item.code }}</td>\r\n                <td>{{ item.name }}</td>\r\n                <td>\r\n                  <el-input\r\n                    v-model=\"item.memo\"\r\n                    size=\"mini\"\r\n                    placeholder=\"请输入网点备注\"\r\n                    maxlength=\"200\"\r\n                    show-word-limit\r\n                    clearable\r\n                    style=\"width: 100%;\"\r\n                  />\r\n                </td>\r\n                <td>\r\n                  <el-button type=\"danger\" size=\"mini\" @click=\"removeBatchRow(index)\">删除</el-button>\r\n                </td>\r\n              </tr>\r\n            </template>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"left: 30px; float: left;\">\r\n          <span style=\"color: #909399; font-size: 12px;\">\r\n            共 {{ batchUploadList.length }} 条数据，其中 {{ getBatchErrorCount() }} 条错误\r\n          </span>\r\n        </div>\r\n        <el-button size=\"small\" @click=\"batchUploadVisible = false\">取 消</el-button>\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitBatchUpdate\">确定更新</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <map-location ref=\"mapLocation\" @success=\"pined\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { Loading } from 'element-ui'\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport Region from '@/views/components/Region.vue'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport UserChosen from '@/views/components/UserChosen.vue'\r\nimport MapLocation from '@/views/map/util/location.vue'\r\nimport UploadFile from '@/views/components/UploadFile.vue'\r\n\r\nexport default {\r\n  components: { PageTable, Region, TreeBox, UserChosen, MapLocation, UploadFile },\r\n  data() {\r\n    return {\r\n      regionList: [],\r\n      deptTree: [], // 机构数据列表\r\n      activeItem: {},\r\n      tableHeight: 300,\r\n      regionVisible: false,\r\n      regionData: { region: '' },\r\n      regionRules: {\r\n        code: [{ required: true, message: '请输入区域编码', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],\r\n        dept: [{ required: true, message: '请选择所属机构', trigger: 'blur' }],\r\n        region: [{ required: true, message: '请选择所在区划', trigger: 'blur' }]\r\n      },\r\n      qform: { keyword: '' },\r\n      locationVisible: false,\r\n      locationData: { region: '' },\r\n      locationRules: {\r\n        code: [{ required: true, message: '请输入地点编码', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入地点名称', trigger: 'blur' }]\r\n      },\r\n      fileList: [],\r\n      uploadList: [],\r\n      uploadVisible: false,\r\n      showUploadAll: true,\r\n      amLocationAsset: [{}],\r\n      // 批量修改相关\r\n      batchFileList: [],\r\n      batchUploadList: [],\r\n      batchUploadVisible: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadDeptTree()\r\n    this.loadRegion()\r\n    this.searchLocation()\r\n    this.$refs.grid.setMaxHeight(Math.max(document.documentElement.clientHeight - 380, 200))\r\n  },\r\n  methods: {\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return ''\r\n      const date = new Date(dateTime)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    loadRegion() {\r\n      this.$http('/am/region/list').then(res => {\r\n        this.regionList = res || []\r\n        if (this.activeItem && this.activeItem.id) {\r\n          for (let i = 0; i < res.length; i++) {\r\n            if (res[i].id === this.activeItem.id) {\r\n              this.activeItem = res[i]\r\n              break\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    showRegion(item) {\r\n      this.activeItem = item\r\n      this.qform.region = item.id\r\n      this.qform.keyword = ''\r\n      this.searchLocation()\r\n    },\r\n    addRegion() {\r\n      this.regionVisible = true\r\n      this.regionData = { ord: 1 }\r\n    },\r\n    editRegion() {\r\n      if (!this.activeItem.id) return\r\n      this.regionVisible = true\r\n      const json = JSON.stringify(this.activeItem)\r\n      this.regionData = JSON.parse(json)\r\n    },\r\n    saveRegion() {\r\n      this.$refs.regionform.validate(valid => {\r\n        if (valid) {\r\n          this.$http({ url: '/am/region/save', data: this.regionData }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('保存区域信息成功')\r\n              this.regionVisible = false\r\n              this.loadRegion()\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeRegion() {\r\n      this.$confirm('此操作将永久删除该区域, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.$http({ url: '/am/region/delete/' + this.activeItem.id }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('删除成功')\r\n            this.activeItem = {}\r\n            this.loadRegion()\r\n          }\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    searchLocation() {\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    addLocation() {\r\n      this.locationVisible = true\r\n      this.locationData = { region: this.activeItem.id }\r\n      this.amLocationAsset = []\r\n    },\r\n    // editLocation(item) {\r\n    //   this.locationVisible = true\r\n    //   const json = JSON.stringify(item)\r\n    //   this.locationData = JSON.parse(json)\r\n    // },\r\n    editLocation(item) {\r\n      this.$http('/am/location/get/' + item.id).then(res => {\r\n        if (res.code > 0 && res.data) {\r\n          this.locationVisible = true\r\n          this.locationData = res.data\r\n          this.amLocationAsset = res.data.amLocationAsset || []\r\n        }\r\n      })\r\n    },\r\n    saveLocation() {\r\n      this.$refs.locationform.validate(valid => {\r\n        if (valid) {\r\n          const details = []\r\n          this.amLocationAsset.forEach(r => details.push({ sn: r.sn }))\r\n          if (!details.length) return this.$message.warning('请录入终端信息')\r\n          this.locationData.amLocationAsset = details\r\n          this.$http({ url: '/am/location/saveDevice', data: this.locationData }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('保存区域信息成功')\r\n              this.locationVisible = false\r\n              this.searchLocation()\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeLocation(item) {\r\n      this.$confirm('此操作将永久删除该地点, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.$http({ url: '/am/location/delete/' + item.id }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('删除成功')\r\n            this.searchLocation()\r\n          }\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    mapPin() {\r\n      const ll = this.locationData.lat ? { lng: this.locationData.lng, lat: this.locationData.lat } : null\r\n      this.$refs.mapLocation.show(ll)\r\n    },\r\n    pined(r) {\r\n      this.$set(this.locationData, 'address', r.address)\r\n      this.$set(this.locationData, 'lng', r.lnglat ? r.lnglat.lng : null)\r\n      this.$set(this.locationData, 'lat', r.lnglat ? r.lnglat.lat : null)\r\n    },\r\n    upload() {\r\n      this.fileList = []\r\n      this.uploadList = []\r\n      this.uploadVisible = true\r\n    },\r\n    uploadRemove() {\r\n      this.uploadList = []\r\n    },\r\n    toggleErr() {\r\n      this.showUploadAll = !this.showUploadAll\r\n    },\r\n    uploaded(fileList) {\r\n      if (fileList && fileList.length) {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })\r\n        this.$http({ url: '/am/location/uploadFile', data: fileList[0] }).then(res => {\r\n          if (res.code > 0) {\r\n            this.showUploadAll = true\r\n            this.uploadList = res.data\r\n          }\r\n          loadInst.close()\r\n        })\r\n      }\r\n    },\r\n    submitUpload() {\r\n      if (this.uploadList.length === 0) return this.$message.warning('没有可提交的数据')\r\n      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })\r\n      this.$http({ url: '/am/location/uploadData', data: this.uploadList }).then(res => {\r\n        if (res.code === 1) {\r\n          this.$message.success('上传成功')\r\n          this.$emit('success')\r\n          this.uploadVisible = false\r\n          this.search()\r\n        } else if (res.code === 2) {\r\n          this.uploadList = res.data\r\n          this.$message.error('存在错误的数据行')\r\n        }\r\n        loadInst.close()\r\n      }).catch(() => {\r\n        loadInst.close()\r\n        // this.$message.error('网络超时')\r\n      })\r\n    },\r\n    addAsset() {\r\n      this.amLocationAsset.push({})\r\n    },\r\n    removeAsset(rowIndex) {\r\n      this.amLocationAsset.splice(rowIndex, 1)\r\n    },\r\n    // 批量修改相关方法\r\n    handleBatchCommand(command) {\r\n      if (command === 'export') {\r\n        this.exportBatch()\r\n      } else if (command === 'import') {\r\n        this.importBatch()\r\n      }\r\n    },\r\n    exportBatch() {\r\n      if (!this.activeItem || !this.activeItem.id) {\r\n        return this.$message.warning('请先选择一个区域')\r\n      }\r\n      const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n      this.$jasper({ url: `/am/location/exportBatch/${this.activeItem.id}`, responseType: 'blob' }).then(blob => {\r\n        loadInst.close()\r\n        this.$saveAs(blob, '网点批量修改模板.xlsx')\r\n      }).catch(err => {\r\n        loadInst.close()\r\n        this.$message.error('导出生成出错:' + err)\r\n      })\r\n    },\r\n    importBatch() {\r\n      if (!this.activeItem || !this.activeItem.id) {\r\n        return this.$message.warning('请先选择一个区域')\r\n      }\r\n      this.batchFileList = []\r\n      this.batchUploadList = []\r\n      this.batchUploadVisible = true\r\n    },\r\n    batchUploadRemove() {\r\n      this.batchUploadList = []\r\n    },\r\n    removeBatchRow(index) {\r\n      this.$confirm('确定要删除这条数据吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchUploadList.splice(index, 1)\r\n        this.$message.success('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    getBatchErrorCount() {\r\n      return this.batchUploadList.filter(item => item.rowMsg != null).length\r\n    },\r\n    batchUploaded(fileList) {\r\n      if (fileList && fileList.length) {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })\r\n        this.$http({ url: '/am/location/uploadBatchFile', data: fileList[0] }).then(res => {\r\n          if (res.code > 0) {\r\n            this.batchUploadList = res.data\r\n          }\r\n          loadInst.close()\r\n        })\r\n      }\r\n    },\r\n    submitBatchUpdate() {\r\n      if (this.batchUploadList.length === 0) return this.$message.warning('没有可提交的数据')\r\n\r\n      // 过滤出没有错误的数据\r\n      const validData = this.batchUploadList.filter(item => !item.rowMsg)\r\n      if (validData.length === 0) {\r\n        return this.$message.warning('没有有效的数据可以提交，请先删除错误行或修正数据')\r\n      }\r\n\r\n      const errorCount = this.getBatchErrorCount()\r\n      if (errorCount > 0) {\r\n        this.$confirm(`当前有 ${errorCount} 条错误数据将被忽略，只提交 ${validData.length} 条有效数据，是否继续？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.checkDuplicatesAndUpdate(validData)\r\n        }).catch(() => {})\r\n      } else {\r\n        this.checkAndConfirmDuplicates(validData)\r\n      }\r\n    },\r\n    checkAndConfirmDuplicates(data) {\r\n      // 检查重复编码\r\n      this.$http({ url: '/am/location/checkBatchDuplicates', data: data }).then(res => {\r\n        if (res.code === 1 && res.data) {\r\n          const duplicateInfo = res.data\r\n          if (duplicateInfo.hasDuplicates) {\r\n            let message = ''\r\n            const importDuplicates = duplicateInfo.importDuplicates || []\r\n            const dbDuplicates = duplicateInfo.dbDuplicates || []\r\n\r\n            if (importDuplicates.length > 0) {\r\n              message += `检测到以下网点编码在导入数据中重复出现：${importDuplicates.join(', ')}。\\n`\r\n            }\r\n\r\n            if (dbDuplicates.length > 0) {\r\n              message += `检测到以下网点编码在数据库中存在多条记录：${dbDuplicates.join(', ')}。\\n`\r\n            }\r\n            message += `重复编码将以最后一条数据为准\\n`\r\n            message += `是否继续执行批量更新？`\r\n\r\n            this.$confirm(message, '发现重复编码', {\r\n              confirmButtonText: '确定更新',\r\n              cancelButtonText: '取消',\r\n              type: 'warning',\r\n              dangerouslyUseHTMLString: false\r\n            }).then(() => {\r\n              this.doBatchUpdate(data)\r\n            }).catch(() => {\r\n              // 用户取消，不执行更新\r\n            })\r\n          } else {\r\n            // 没有重复编码，直接更新\r\n            this.doBatchUpdate(data)\r\n          }\r\n        } else {\r\n          // 检查失败，直接更新\r\n          this.doBatchUpdate(data)\r\n        }\r\n      }).catch(() => {\r\n        // 检查失败，直接更新\r\n        this.doBatchUpdate(data)\r\n      })\r\n    },\r\n    doBatchUpdate(data) {\r\n      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })\r\n      this.$http({ url: '/am/location/batchUpdate', data: data }).then(res => {\r\n        if (res.code === 1) {\r\n          this.$message.success(`批量更新成功`)\r\n          this.batchUploadVisible = false\r\n          this.searchLocation()\r\n        } else if (res.code === 2) {\r\n          this.batchUploadList = res.data\r\n          this.$message.error('部分数据更新失败，请查看错误信息')\r\n        }\r\n        loadInst.close()\r\n      }).catch(() => {\r\n        loadInst.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang='scss' scope>\r\n.layout-lr {\r\n  height: calc(100vh - 52px);\r\n}\r\n\r\n.layout-lr .left {\r\n  min-width: 220px;\r\n}\r\n\r\n.layout-lr .center {\r\n  overflow: hidden;\r\n}\r\n\r\n.location-head {\r\n  height: 40px;\r\n}\r\n\r\n.location-title {\r\n  line-height: 32px;\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  color: #303133;\r\n}\r\n\r\n.location-search {\r\n  width: 300px;\r\n  float: right;\r\n}\r\n\r\n.upload-block {\r\n  padding: 0 4px;\r\n  width: calc(100vw - 12px);\r\n  height: calc(100vh - 250px);\r\n  overflow: auto;\r\n}\r\n\r\n.upload-block table {\r\n  border: 1px solid #cccccc;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.upload-block table th,\r\n.upload-block table td {\r\n  border: 1px solid #cccccc;\r\n  border-collapse: collapse;\r\n  padding: 4px 6px;\r\n  font-size: 12px;\r\n  line-height: 18px;\r\n}\r\n\r\n.upload-block table tr.err {\r\n  background-color: #faee92;\r\n}\r\n</style>\r\n"]}]}