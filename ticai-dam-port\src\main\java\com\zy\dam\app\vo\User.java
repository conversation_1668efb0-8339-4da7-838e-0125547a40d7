package com.zy.dam.app.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zy.model.BaseUser;
import com.zy.model.TypeKeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "登录用户信息")
public class User extends BaseUser {

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色ID列表")
    private String roleIds;

    @Schema(description = "菜单列表")
    private List<Menu> menus;

    @Schema(description = "操作按钮列表")
    private List<String> opts;

    @Schema(description = "上级机构")
    private String parentDept;

    @Schema(description = "机构类型：1-中心，2-片区，3-站级，4-检测站，5-气库")
    private String deptType;

    @Schema(description = "包含机构ID列表")
    private List<String> deptIds;

    @Schema(description = "权限编码")
    private String privilege;

    @Schema(description = "附加权限编码列表")
    private String[] otherPrivileges;


    @Schema(description = "所有字典列表，登录后携带，不存入redis")
    private List<TypeKeyValue> dictCodes;

    @Schema(description = "附件上下文")
    private String attachContext;

    @Schema(description = "是否为超级管理员")
    private Boolean isSuperAdmin;

    /**
     * 气站上级
     *
     * @return
     */
    @Schema(hidden = true)
    @JsonIgnore
    public boolean isStationParent() {
        return "1".equals(deptType) || "2".equals(deptType);
    }

    @Schema(hidden = true)
    @JsonIgnore
    public boolean isAdmin() {
        return "admin".equals(getAccount());
    }
}
