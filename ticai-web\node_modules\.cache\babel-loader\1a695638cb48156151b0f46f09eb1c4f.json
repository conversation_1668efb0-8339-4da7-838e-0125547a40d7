{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\work\\ticai\\ticai-web\\src\\store\\modules\\user.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\store\\modules\\user.js", "mtime": 1753321306919}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js", "mtime": 1747730939696}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiOwppbXBvcnQgeyByZW1vdmVUb2tlbiwgc2V0VG9rZW4gfSBmcm9tICdAL3V0aWxzL2F1dGgnOwppbXBvcnQgcmVxdWVzdCBmcm9tICdAL3V0aWxzL3JlcXVlc3QnOwp2YXIgZ2V0RGVmYXVsdFN0YXRlID0gZnVuY3Rpb24gZ2V0RGVmYXVsdFN0YXRlKCkgewogIHJldHVybiB7CiAgICBhdHRhY2hDb250ZXh0OiAnL2F0dGFjaCcsCiAgICAvLyDpmYTku7bkuIrkuIvmlocKICAgIHRva2VuOiAnJywKICAgIC8vIOeZu+W9leS7pOeJjAogICAgaW5mbzoge30sCiAgICAvLyDnlKjmiLfkv6Hmga8KICAgIG1lbnVzOiBbXSwKICAgIC8vIOiPnOWNlQogICAgZGljdDoge30sCiAgICAvLyDlrZflhbgKICAgIHN5c3RlbUNvbmZpZzoge30gLy8g57O757uf6YWN572uCiAgfTsKfTsKdmFyIHN0YXRlID0gZ2V0RGVmYXVsdFN0YXRlKCk7CnZhciBtdXRhdGlvbnMgPSB7CiAgUkVTRVRfU1RBVEU6IGZ1bmN0aW9uIFJFU0VUX1NUQVRFKHN0YXRlKSB7CiAgICBPYmplY3QuYXNzaWduKHN0YXRlLCBnZXREZWZhdWx0U3RhdGUoKSk7CiAgfSwKICBTRVRfVVNFUjogZnVuY3Rpb24gU0VUX1VTRVIoc3RhdGUsIHVzZXIpIHsKICAgIGlmICh1c2VyLmF0dGFjaENvbnRleHQpIHN0YXRlLmF0dGFjaENvbnRleHQgPSB1c2VyLmF0dGFjaENvbnRleHQ7CiAgICBzdGF0ZS50b2tlbiA9IHVzZXIudG9rZW47CiAgICBzdGF0ZS5pbmZvID0gdXNlcjsKICAgIHN0YXRlLm1lbnVzID0gdXNlci5tZW51czsKICAgIHZhciBkaWN0ID0ge307CiAgICBpZiAodXNlci5kaWN0Q29kZXMpIHsKICAgICAgdmFyIGNvZGVzOwogICAgICB1c2VyLmRpY3RDb2Rlcy5mb3JFYWNoKGZ1bmN0aW9uIChyKSB7CiAgICAgICAgY29kZXMgPSBkaWN0W3IudHlwZV07CiAgICAgICAgaWYgKCFjb2RlcykgZGljdFtyLnR5cGVdID0gY29kZXMgPSBbXTsKICAgICAgICBjb2Rlcy5wdXNoKHsKICAgICAgICAgIHZhbHVlOiByLmtleSwKICAgICAgICAgIHRleHQ6IHIudmFsdWUKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9CiAgICBzdGF0ZS5kaWN0ID0gZGljdDsKICB9LAogIFNFVF9TWVNURU1fQ09ORklHOiBmdW5jdGlvbiBTRVRfU1lTVEVNX0NPTkZJRyhzdGF0ZSwgY29uZmlnKSB7CiAgICBzdGF0ZS5zeXN0ZW1Db25maWcgPSBjb25maWc7CiAgfQp9Owp2YXIgYWN0aW9ucyA9IHsKICAvLyB1c2VyIGxvZ2luCiAgbG9naW46IGZ1bmN0aW9uIGxvZ2luKF9yZWYsIGRhdGEpIHsKICAgIHZhciBjb21taXQgPSBfcmVmLmNvbW1pdDsKICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICAgIHJlcXVlc3QoewogICAgICAgIHVybDogJy9sb2dpbicsCiAgICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgICAgZGF0YTogZGF0YQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmNvZGUgPiAwICYmIHJlcy5kYXRhKSB7CiAgICAgICAgICBjb21taXQoJ1NFVF9VU0VSJywgcmVzLmRhdGEpOwogICAgICAgICAgc2V0VG9rZW4ocmVzLmRhdGEudG9rZW4pOwogICAgICAgICAgcmVzb2x2ZShyZXMpOwogICAgICAgIH0gZWxzZSByZWplY3QoJ+eZu+W9leWksei0pScpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgcmVqZWN0KCfnmbvlvZXlvILluLgnKTsKICAgICAgfSk7CiAgICB9KTsKICB9LAogIC8vIHVzZXIgbG9nb3V0CiAgbG9nb3V0OiBmdW5jdGlvbiBsb2dvdXQoX3JlZjIpIHsKICAgIHZhciBjb21taXQgPSBfcmVmMi5jb21taXQ7CiAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgewogICAgICByZXF1ZXN0KCcvbG9nb3V0JykudGhlbihmdW5jdGlvbiAocikgewogICAgICAgIC8vIHJlc2V0Um91dGVyKCkKICAgICAgICBjb21taXQoJ1JFU0VUX1NUQVRFJyk7CiAgICAgICAgcmVtb3ZlVG9rZW4oKTsKICAgICAgICByZXNvbHZlKCk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIHJlamVjdChlcnJvcik7CiAgICAgIH0pOwogICAgfSk7CiAgfSwKICBnZXQ6IGZ1bmN0aW9uIGdldChfcmVmMywgdG9rZW4pIHsKICAgIHZhciBjb21taXQgPSBfcmVmMy5jb21taXQ7CiAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgewogICAgICAvLyDpnZnpu5jkuI3pnIDopoHplJnor6/mj5DnpLoKICAgICAgcmVxdWVzdCh7CiAgICAgICAgdXJsOiAnL3VzZXInLAogICAgICAgIHNpbGVudDogdHJ1ZQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyKSB7CiAgICAgICAgaWYgKHIuY29kZSA+IDAgJiYgci5kYXRhKSB7CiAgICAgICAgICBjb21taXQoJ1NFVF9VU0VSJywgci5kYXRhKTsKICAgICAgICAgIHNldFRva2VuKHIuZGF0YS50b2tlbik7CiAgICAgICAgICByZXNvbHZlKHIpOwogICAgICAgIH0gZWxzZSByZWplY3QoJ+aXoOazleiOt+WPlueUqOaItycpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICByZWplY3QoZXJyb3IpOwogICAgICB9KTsKICAgIH0pOwogIH0sCiAgLy8gcmVtb3ZlIHRva2VuCiAgcmVzZXRUb2tlbjogZnVuY3Rpb24gcmVzZXRUb2tlbihfcmVmNCkgewogICAgdmFyIGNvbW1pdCA9IF9yZWY0LmNvbW1pdDsKICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSkgewogICAgICBjb21taXQoJ1JFU0VUX1NUQVRFJyk7CiAgICAgIHJlbW92ZVRva2VuKCk7CiAgICAgIHJlc29sdmUoKTsKICAgIH0pOwogIH0sCiAgLy8gbG9hZCBzeXN0ZW0gY29uZmlnCiAgbG9hZFN5c3RlbUNvbmZpZzogZnVuY3Rpb24gbG9hZFN5c3RlbUNvbmZpZyhfcmVmNSkgewogICAgdmFyIGNvbW1pdCA9IF9yZWY1LmNvbW1pdDsKICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICAgIHJlcXVlc3QoewogICAgICAgIHVybDogJy9hcHAvY29uZmlnJywKICAgICAgICBzaWxlbnQ6IHRydWUKICAgICAgfSkudGhlbihmdW5jdGlvbiAocikgewogICAgICAgIGlmIChyLmNvZGUgPiAwICYmIHIuZGF0YSkgewogICAgICAgICAgY29tbWl0KCdTRVRfU1lTVEVNX0NPTkZJRycsIHIuZGF0YSk7CiAgICAgICAgICByZXNvbHZlKHIuZGF0YSk7CiAgICAgICAgfSBlbHNlIHJlamVjdCgn5peg5rOV6I635Y+W57O757uf6YWN572uJyk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIHJlamVjdChlcnJvcik7CiAgICAgIH0pOwogICAgfSk7CiAgfQp9OwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZXNwYWNlZDogdHJ1ZSwKICBzdGF0ZTogc3RhdGUsCiAgbXV0YXRpb25zOiBtdXRhdGlvbnMsCiAgYWN0aW9uczogYWN0aW9ucwp9Ow=="}, {"version": 3, "names": ["removeToken", "setToken", "request", "getDefaultState", "attachContext", "token", "info", "menus", "dict", "systemConfig", "state", "mutations", "RESET_STATE", "Object", "assign", "SET_USER", "user", "dictCodes", "codes", "for<PERSON>ach", "r", "type", "push", "value", "key", "text", "SET_SYSTEM_CONFIG", "config", "actions", "login", "_ref", "data", "commit", "Promise", "resolve", "reject", "url", "method", "then", "res", "code", "catch", "logout", "_ref2", "error", "get", "_ref3", "silent", "resetToken", "_ref4", "loadSystemConfig", "_ref5", "namespaced"], "sources": ["F:/work/ticai/ticai-web/src/store/modules/user.js"], "sourcesContent": ["import { removeToken, setToken } from '@/utils/auth'\r\nimport request from '@/utils/request'\r\n\r\nconst getDefaultState = () => {\r\n  return {\r\n    attachContext: '/attach', // 附件上下文\r\n    token: '', // 登录令牌\r\n    info: {}, // 用户信息\r\n    menus: [], // 菜单\r\n    dict: {}, // 字典\r\n    systemConfig: {} // 系统配置\r\n  }\r\n}\r\n\r\nconst state = getDefaultState()\r\n\r\nconst mutations = {\r\n  RESET_STATE: (state) => {\r\n    Object.assign(state, getDefaultState())\r\n  },\r\n  SET_USER: (state, user) => {\r\n    if (user.attachContext) state.attachContext = user.attachContext\r\n    state.token = user.token\r\n    state.info = user\r\n    state.menus = user.menus\r\n    var dict = {}\r\n    if (user.dictCodes) {\r\n      var codes\r\n      user.dictCodes.forEach(r => {\r\n        codes = dict[r.type]\r\n        if (!codes) dict[r.type] = codes = []\r\n        codes.push({ value: r.key, text: r.value })\r\n      })\r\n    }\r\n    state.dict = dict\r\n  },\r\n  SET_SYSTEM_CONFIG: (state, config) => {\r\n    state.systemConfig = config\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  // user login\r\n  login({ commit }, data) {\r\n    return new Promise((resolve, reject) => {\r\n      request({ url: '/login', method: 'post', data: data }).then(res => {\r\n        if (res.code > 0 && res.data) {\r\n          commit('SET_USER', res.data)\r\n          setToken(res.data.token)\r\n          resolve(res)\r\n        } else reject('登录失败')\r\n      }).catch(() => {\r\n        reject('登录异常')\r\n      })\r\n    })\r\n  },\r\n  // user logout\r\n  logout({ commit }) {\r\n    return new Promise((resolve, reject) => {\r\n      request('/logout').then(r => {\r\n        // resetRouter()\r\n        commit('RESET_STATE')\r\n        removeToken()\r\n        resolve()\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  },\r\n  get({ commit }, token) {\r\n    return new Promise((resolve, reject) => {\r\n      // 静默不需要错误提示\r\n      request({ url: '/user', silent: true }).then(r => {\r\n        if (r.code > 0 && r.data) {\r\n          commit('SET_USER', r.data)\r\n          setToken(r.data.token)\r\n          resolve(r)\r\n        } else reject('无法获取用户')\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  },\r\n  // remove token\r\n  resetToken({ commit }) {\r\n    return new Promise(resolve => {\r\n      commit('RESET_STATE')\r\n      removeToken()\r\n      resolve()\r\n    })\r\n  },\r\n  // load system config\r\n  loadSystemConfig({ commit }) {\r\n    return new Promise((resolve, reject) => {\r\n      request({ url: '/app/config', silent: true }).then(r => {\r\n        if (r.code > 0 && r.data) {\r\n          commit('SET_SYSTEM_CONFIG', r.data)\r\n          resolve(r.data)\r\n        } else reject('无法获取系统配置')\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n\r\n"], "mappings": ";;;;AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,cAAc;AACpD,OAAOC,OAAO,MAAM,iBAAiB;AAErC,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EAC5B,OAAO;IACLC,aAAa,EAAE,SAAS;IAAE;IAC1BC,KAAK,EAAE,EAAE;IAAE;IACXC,IAAI,EAAE,CAAC,CAAC;IAAE;IACVC,KAAK,EAAE,EAAE;IAAE;IACXC,IAAI,EAAE,CAAC,CAAC;IAAE;IACVC,YAAY,EAAE,CAAC,CAAC,CAAC;EACnB,CAAC;AACH,CAAC;AAED,IAAMC,KAAK,GAAGP,eAAe,CAAC,CAAC;AAE/B,IAAMQ,SAAS,GAAG;EAChBC,WAAW,EAAE,SAAbA,WAAWA,CAAGF,KAAK,EAAK;IACtBG,MAAM,CAACC,MAAM,CAACJ,KAAK,EAAEP,eAAe,CAAC,CAAC,CAAC;EACzC,CAAC;EACDY,QAAQ,EAAE,SAAVA,QAAQA,CAAGL,KAAK,EAAEM,IAAI,EAAK;IACzB,IAAIA,IAAI,CAACZ,aAAa,EAAEM,KAAK,CAACN,aAAa,GAAGY,IAAI,CAACZ,aAAa;IAChEM,KAAK,CAACL,KAAK,GAAGW,IAAI,CAACX,KAAK;IACxBK,KAAK,CAACJ,IAAI,GAAGU,IAAI;IACjBN,KAAK,CAACH,KAAK,GAAGS,IAAI,CAACT,KAAK;IACxB,IAAIC,IAAI,GAAG,CAAC,CAAC;IACb,IAAIQ,IAAI,CAACC,SAAS,EAAE;MAClB,IAAIC,KAAK;MACTF,IAAI,CAACC,SAAS,CAACE,OAAO,CAAC,UAAAC,CAAC,EAAI;QAC1BF,KAAK,GAAGV,IAAI,CAACY,CAAC,CAACC,IAAI,CAAC;QACpB,IAAI,CAACH,KAAK,EAAEV,IAAI,CAACY,CAAC,CAACC,IAAI,CAAC,GAAGH,KAAK,GAAG,EAAE;QACrCA,KAAK,CAACI,IAAI,CAAC;UAAEC,KAAK,EAAEH,CAAC,CAACI,GAAG;UAAEC,IAAI,EAAEL,CAAC,CAACG;QAAM,CAAC,CAAC;MAC7C,CAAC,CAAC;IACJ;IACAb,KAAK,CAACF,IAAI,GAAGA,IAAI;EACnB,CAAC;EACDkB,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGhB,KAAK,EAAEiB,MAAM,EAAK;IACpCjB,KAAK,CAACD,YAAY,GAAGkB,MAAM;EAC7B;AACF,CAAC;AAED,IAAMC,OAAO,GAAG;EACd;EACAC,KAAK,WAALA,KAAKA,CAAAC,IAAA,EAAaC,IAAI,EAAE;IAAA,IAAhBC,MAAM,GAAAF,IAAA,CAANE,MAAM;IACZ,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtCjC,OAAO,CAAC;QAAEkC,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEN,IAAI,EAAEA;MAAK,CAAC,CAAC,CAACO,IAAI,CAAC,UAAAC,GAAG,EAAI;QACjE,IAAIA,GAAG,CAACC,IAAI,GAAG,CAAC,IAAID,GAAG,CAACR,IAAI,EAAE;UAC5BC,MAAM,CAAC,UAAU,EAAEO,GAAG,CAACR,IAAI,CAAC;UAC5B9B,QAAQ,CAACsC,GAAG,CAACR,IAAI,CAAC1B,KAAK,CAAC;UACxB6B,OAAO,CAACK,GAAG,CAAC;QACd,CAAC,MAAMJ,MAAM,CAAC,MAAM,CAAC;MACvB,CAAC,CAAC,CAACM,KAAK,CAAC,YAAM;QACbN,MAAM,CAAC,MAAM,CAAC;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAO,MAAM,WAANA,MAAMA,CAAAC,KAAA,EAAa;IAAA,IAAVX,MAAM,GAAAW,KAAA,CAANX,MAAM;IACb,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtCjC,OAAO,CAAC,SAAS,CAAC,CAACoC,IAAI,CAAC,UAAAlB,CAAC,EAAI;QAC3B;QACAY,MAAM,CAAC,aAAa,CAAC;QACrBhC,WAAW,CAAC,CAAC;QACbkC,OAAO,CAAC,CAAC;MACX,CAAC,CAAC,CAACO,KAAK,CAAC,UAAAG,KAAK,EAAI;QAChBT,MAAM,CAACS,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDC,GAAG,WAAHA,GAAGA,CAAAC,KAAA,EAAazC,KAAK,EAAE;IAAA,IAAjB2B,MAAM,GAAAc,KAAA,CAANd,MAAM;IACV,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtC;MACAjC,OAAO,CAAC;QAAEkC,GAAG,EAAE,OAAO;QAAEW,MAAM,EAAE;MAAK,CAAC,CAAC,CAACT,IAAI,CAAC,UAAAlB,CAAC,EAAI;QAChD,IAAIA,CAAC,CAACoB,IAAI,GAAG,CAAC,IAAIpB,CAAC,CAACW,IAAI,EAAE;UACxBC,MAAM,CAAC,UAAU,EAAEZ,CAAC,CAACW,IAAI,CAAC;UAC1B9B,QAAQ,CAACmB,CAAC,CAACW,IAAI,CAAC1B,KAAK,CAAC;UACtB6B,OAAO,CAACd,CAAC,CAAC;QACZ,CAAC,MAAMe,MAAM,CAAC,QAAQ,CAAC;MACzB,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAG,KAAK,EAAI;QAChBT,MAAM,CAACS,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAI,UAAU,WAAVA,UAAUA,CAAAC,KAAA,EAAa;IAAA,IAAVjB,MAAM,GAAAiB,KAAA,CAANjB,MAAM;IACjB,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BF,MAAM,CAAC,aAAa,CAAC;MACrBhC,WAAW,CAAC,CAAC;MACbkC,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ,CAAC;EACD;EACAgB,gBAAgB,WAAhBA,gBAAgBA,CAAAC,KAAA,EAAa;IAAA,IAAVnB,MAAM,GAAAmB,KAAA,CAANnB,MAAM;IACvB,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtCjC,OAAO,CAAC;QAAEkC,GAAG,EAAE,aAAa;QAAEW,MAAM,EAAE;MAAK,CAAC,CAAC,CAACT,IAAI,CAAC,UAAAlB,CAAC,EAAI;QACtD,IAAIA,CAAC,CAACoB,IAAI,GAAG,CAAC,IAAIpB,CAAC,CAACW,IAAI,EAAE;UACxBC,MAAM,CAAC,mBAAmB,EAAEZ,CAAC,CAACW,IAAI,CAAC;UACnCG,OAAO,CAACd,CAAC,CAACW,IAAI,CAAC;QACjB,CAAC,MAAMI,MAAM,CAAC,UAAU,CAAC;MAC3B,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAG,KAAK,EAAI;QAChBT,MAAM,CAACS,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAe;EACbQ,UAAU,EAAE,IAAI;EAChB1C,KAAK,EAALA,KAAK;EACLC,SAAS,EAATA,SAAS;EACTiB,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}