{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\work\\ticai\\ticai-web\\src\\router\\index.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\router\\index.js", "mtime": 1753320296339}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js", "mtime": 1747730939696}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "Router", "use", "Layout", "constantRoutes", "path", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "redirect", "children", "name", "hidden", "createRouter", "scroll<PERSON>eh<PERSON>or", "y", "routes", "router", "resetRouter", "newRouter", "matcher"], "sources": ["F:/work/ticai/ticai-web/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport Router from 'vue-router'\r\n\r\nVue.use(Router)\r\n\r\n/* Layout */\r\nimport Layout from '@/layout'\r\n\r\nexport const constantRoutes = [\r\n  {\r\n    path: '/login',\r\n    component: () => import('@/views/login')\r\n  },\r\n  {\r\n    path: '/404',\r\n    component: () => import('@/views/404')\r\n  },\r\n  {\r\n    path: '/formDesigner',\r\n    meta: { title: '表单设置' },\r\n    component: () => import('@/views/form/designer')\r\n  },\r\n  {\r\n    path: '/formSimple',\r\n    meta: { title: '表单设置' },\r\n    component: () => import('@/views/form/simple')\r\n  },\r\n  {\r\n    path: '/',\r\n    component: Layout,\r\n    redirect: '/dashboard',\r\n    children: [{\r\n      path: 'dashboard',\r\n      meta: { name: '首页' },\r\n      component: () => import('@/views/dashboard')\r\n    }]\r\n  },\r\n  {\r\n    path: '/main',\r\n    component: Layout,\r\n    children: [\r\n      {\r\n        path: 'profile',\r\n        meta: { name: '个人中心' },\r\n        component: () => import('@/views/main/profile')\r\n      },\r\n      {\r\n        path: 'qa',\r\n        meta: { name: '问题' },\r\n        component: () => import('@/views/main/qa')\r\n      },\r\n      {\r\n        path: 'icon',\r\n        meta: { name: '图标' },\r\n        component: () => import('@/views/main/icon')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/sys', // 系统管理\r\n    component: Layout,\r\n    children: [\r\n      {\r\n        path: 'dept',\r\n        meta: { name: '组织机构' },\r\n        component: () => import('@/views/sys/dept')\r\n      },\r\n      {\r\n        path: 'role',\r\n        meta: { name: '角色管理' },\r\n        component: () => import('@/views/sys/role')\r\n      },\r\n      {\r\n        path: 'user',\r\n        meta: { name: '用户管理' },\r\n        component: () => import('@/views/sys/user')\r\n      },\r\n      {\r\n        path: 'dict',\r\n        meta: { name: '字典管理' },\r\n        component: () => import('@/views/sys/dict')\r\n      },\r\n      {\r\n        path: 'region',\r\n        meta: { name: '区划编码' },\r\n        component: () => import('@/views/sys/region')\r\n      },\r\n      {\r\n        path: 'log',\r\n        meta: { name: '日志管理' },\r\n        component: () => import('@/views/sys/log')\r\n      },\r\n      {\r\n        path: 'port',\r\n        meta: { name: '接口应用' },\r\n        component: () => import('@/views/sys/port')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/asset', // 资产信息\r\n    component: Layout,\r\n    children: [\r\n      {\r\n        path: 'type',\r\n        meta: { name: '资产类型' },\r\n        component: () => import('@/views/asset/type')\r\n      },\r\n      {\r\n        path: 'account',\r\n        meta: { name: '资产台账' },\r\n        component: () => import('@/views/asset/account')\r\n      },\r\n      {\r\n        path: 'allocate',\r\n        meta: { name: '资产调拨' },\r\n        component: () => import('@/views/asset/allocate')\r\n      },\r\n      {\r\n        path: 'consuming',\r\n        meta: { name: '资产领用' },\r\n        component: () => import('@/views/asset/consuming')\r\n      },\r\n      {\r\n        path: 'deposit',\r\n        meta: { name: '资产押金' },\r\n        component: () => import('@/views/asset/deposit')\r\n      },\r\n      {\r\n        path: 'transfer',\r\n        meta: { name: '资产移交' },\r\n        component: () => import('@/views/asset/transfer')\r\n      },\r\n      {\r\n        path: 'change',\r\n        meta: { name: '资产变更' },\r\n        component: () => import('@/views/asset/change')\r\n      },\r\n      {\r\n        path: 'inventory',\r\n        meta: { name: '资产盘点' },\r\n        component: () => import('@/views/asset/inventory')\r\n      },\r\n      {\r\n        path: 'back',\r\n        meta: { name: '资产退库' },\r\n        component: () => import('@/views/asset/back')\r\n      },\r\n      {\r\n        path: 'maintain',\r\n        meta: { name: '维修登记' },\r\n        component: () => import('@/views/asset/maintain')\r\n      },\r\n      {\r\n        path: 'scrap',\r\n        meta: { name: '清理报废' },\r\n        component: () => import('@/views/asset/scrap')\r\n      },\r\n      {\r\n        path: 'asset-trace',\r\n        meta: { name: '设备管理查询' },\r\n        component: () => import('@/views/asset/asset-trace')\r\n      },\r\n      {\r\n        path: 'asset-trace',\r\n        meta: { name: '资产管理查询' },\r\n        component: () => import('@/views/asset/asset-trace')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/patrol', // 巡检\r\n    component: Layout,\r\n    children: [\r\n      {\r\n        path: 'kpi',\r\n        meta: { name: '巡检指标' },\r\n        component: () => import('@/views/patrol/kpi')\r\n      },\r\n      {\r\n        path: 'point',\r\n        meta: { name: '巡检点位' },\r\n        component: () => import('@/views/patrol/point')\r\n      },\r\n      {\r\n        path: 'asset',\r\n        meta: { name: '巡检资产' },\r\n        component: () => import('@/views/patrol/asset')\r\n      },\r\n      {\r\n        path: 'path',\r\n        meta: { name: '巡检路线' },\r\n        component: () => import('@/views/patrol/path')\r\n      },\r\n      {\r\n        path: 'plan',\r\n        meta: { name: '巡检计划' },\r\n        component: () => import('@/views/patrol/plan')\r\n      },\r\n      {\r\n        path: 'check',\r\n        meta: { name: '终端设备自检' },\r\n        component: () => import('@/views/patrol/check')\r\n      },\r\n      {\r\n        path: 'task',\r\n        meta: { name: '巡检执行' },\r\n        component: () => import('@/views/patrol/task')\r\n      },\r\n      {\r\n        path: 'abnormal',\r\n        meta: { name: '巡检异常' },\r\n        component: () => import('@/views/patrol/abnormal')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/bd', // 基础管理\r\n    component: Layout,\r\n    children: [\r\n      {\r\n        path: 'no',\r\n        meta: { name: '业务编码' },\r\n        component: () => import('@/views/bd/no')\r\n      },\r\n      {\r\n        path: 'region',\r\n        meta: { name: '区域网点' },\r\n        component: () => import('@/views/bd/region')\r\n      },\r\n      {\r\n        path: 'maintain',\r\n        meta: { name: '维保单位' },\r\n        component: () => import('@/views/bd/maintain')\r\n      },\r\n      {\r\n        path: 'sms',\r\n        meta: { name: '短信服务' },\r\n        component: () => import('@/views/bd/sms')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/dp', // 大屏\r\n    component: () => import('@/views/dp'),\r\n    redirect: '/dp/home',\r\n    children: [\r\n      {\r\n        name: 'DpHome',\r\n        path: 'home',\r\n        meta: { name: '大屏首页' },\r\n        component: () => import('@/views/dp/home')\r\n      },\r\n      {\r\n        name: 'DpAsset',\r\n        path: 'asset',\r\n        meta: { name: '资产设备' },\r\n        component: () => import('@/views/dp/asset')\r\n      },\r\n      {\r\n        name: 'DpDistr',\r\n        path: 'distri',\r\n        meta: { name: '资产分布' },\r\n        component: () => import('@/views/dp/distri')\r\n      },\r\n      {\r\n        name: 'DpPoint',\r\n        path: 'point',\r\n        meta: { name: '点位分布' },\r\n        component: () => import('@/views/dp/point')\r\n      },\r\n      {\r\n        name: 'DpPatrol',\r\n        path: 'patrol',\r\n        meta: { name: '巡检统计' },\r\n        component: () => import('@/views/dp/patrol')\r\n      },\r\n      {\r\n        name: 'DpWo',\r\n        path: 'wo',\r\n        meta: { name: '工单统计' },\r\n        component: () => import('@/views/dp/wo')\r\n      },\r\n      {\r\n        name: 'DpMonitor',\r\n        path: 'monitor',\r\n        meta: { name: '实时监控' },\r\n        component: () => import('@/views/dp/monitor')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/wx', // 微信管理\r\n    component: Layout,\r\n    children: [\r\n      {\r\n        path: 'banner',\r\n        meta: { name: '微信横幅' },\r\n        component: () => import('@/views/wx/banner')\r\n      },\r\n      {\r\n        path: 'user',\r\n        meta: { name: '微信用户' },\r\n        component: () => import('@/views/wx/user')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/rp', // 报表\r\n    component: Layout,\r\n    children: [\r\n      {\r\n        path: 'asset/info',\r\n        meta: { name: '资产信息统计' },\r\n        component: () => import('@/views/rp/asset/info')\r\n      },\r\n      {\r\n        path: 'asset/biz',\r\n        meta: { name: '资产增变退统计' },\r\n        component: () => import('@/views/rp/asset/biz')\r\n      },\r\n      {\r\n        path: 'wo/abnormal',\r\n        meta: { name: '故障维护统计' },\r\n        component: () => import('@/views/rp/wo/abnormal')\r\n      },\r\n      {\r\n        path: 'wo/biz',\r\n        meta: { name: '工单统计' },\r\n        component: () => import('@/views/rp/wo/biz')\r\n      },\r\n      {\r\n        path: 'asset/sn',\r\n        meta: { name: '资产终端机绑定统计' },\r\n        component: () => import('@/views/rp/asset/sn')\r\n      },\r\n      {\r\n        path: 'asset/scan',\r\n        meta: { name: '扫码定位统计' },\r\n        component: () => import('@/views/rp/asset/scan')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/wo', // 工单服务\r\n    component: Layout,\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        meta: { name: '工单管理' },\r\n        component: () => import('@/views/wo/index')\r\n      },\r\n      {\r\n        path: 'confirm',\r\n        meta: { name: '工单管理' },\r\n        component: () => import('@/views/wo/confirm')\r\n      }\r\n    ]\r\n  },\r\n  // 404 page must be placed at the end !!!\r\n  { path: '*', redirect: '/404', hidden: true }\r\n]\r\n\r\nconst createRouter = () => new Router({\r\n  // mode: 'history', // require service support\r\n  scrollBehavior: () => ({ y: 0 }),\r\n  routes: constantRoutes\r\n})\r\n\r\nconst router = createRouter()\r\n\r\nexport function resetRouter() {\r\n  const newRouter = createRouter()\r\n  router.matcher = newRouter.matcher // reset router\r\n}\r\n\r\nexport default router\r\n"], "mappings": ";;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,MAAM,MAAM,YAAY;AAE/BD,GAAG,CAACE,GAAG,CAACD,MAAM,CAAC;;AAEf;AACA,OAAOE,MAAM,MAAM,UAAU;AAE7B,OAAO,IAAMC,cAAc,GAAG,CAC5B;EACEC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,eAAe;IAAA;EAAA;AACzC,CAAC,EACD;EACEN,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,aAAa;IAAA;EAAA;AACvC,CAAC,EACD;EACEN,IAAI,EAAE,eAAe;EACrBO,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACvBP,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,uBAAuB;IAAA;EAAA;AACjD,CAAC,EACD;EACEN,IAAI,EAAE,aAAa;EACnBO,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACvBP,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qBAAqB;IAAA;EAAA;AAC/C,CAAC,EACD;EACEN,IAAI,EAAE,GAAG;EACTC,SAAS,EAAEH,MAAM;EACjBW,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,CAAC;IACTV,IAAI,EAAE,WAAW;IACjBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAK,CAAC;IACpBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mBAAmB;MAAA;IAAA;EAC7C,CAAC;AACH,CAAC,EACD;EACEN,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEH,MAAM;EACjBY,QAAQ,EAAE,CACR;IACEV,IAAI,EAAE,SAAS;IACfO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,sBAAsB;MAAA;IAAA;EAChD,CAAC,EACD;IACEN,IAAI,EAAE,IAAI;IACVO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAK,CAAC;IACpBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,iBAAiB;MAAA;IAAA;EAC3C,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAK,CAAC;IACpBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mBAAmB;MAAA;IAAA;EAC7C,CAAC;AAEL,CAAC,EACD;EACEN,IAAI,EAAE,MAAM;EAAE;EACdC,SAAS,EAAEH,MAAM;EACjBY,QAAQ,EAAE,CACR;IACEV,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC,EACD;IACEN,IAAI,EAAE,QAAQ;IACdO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oBAAoB;MAAA;IAAA;EAC9C,CAAC,EACD;IACEN,IAAI,EAAE,KAAK;IACXO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,iBAAiB;MAAA;IAAA;EAC3C,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC;AAEL,CAAC,EACD;EACEN,IAAI,EAAE,QAAQ;EAAE;EAChBC,SAAS,EAAEH,MAAM;EACjBY,QAAQ,EAAE,CACR;IACEV,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oBAAoB;MAAA;IAAA;EAC9C,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,uBAAuB;MAAA;IAAA;EACjD,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,wBAAwB;MAAA;IAAA;EAClD,CAAC,EACD;IACEN,IAAI,EAAE,WAAW;IACjBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,yBAAyB;MAAA;IAAA;EACnD,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,uBAAuB;MAAA;IAAA;EACjD,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,wBAAwB;MAAA;IAAA;EAClD,CAAC,EACD;IACEN,IAAI,EAAE,QAAQ;IACdO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,sBAAsB;MAAA;IAAA;EAChD,CAAC,EACD;IACEN,IAAI,EAAE,WAAW;IACjBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,yBAAyB;MAAA;IAAA;EACnD,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oBAAoB;MAAA;IAAA;EAC9C,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,wBAAwB;MAAA;IAAA;EAClD,CAAC,EACD;IACEN,IAAI,EAAE,OAAO;IACbO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qBAAqB;MAAA;IAAA;EAC/C,CAAC,EACD;IACEN,IAAI,EAAE,aAAa;IACnBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAS,CAAC;IACxBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,2BAA2B;MAAA;IAAA;EACrD,CAAC,EACD;IACEN,IAAI,EAAE,aAAa;IACnBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAS,CAAC;IACxBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,2BAA2B;MAAA;IAAA;EACrD,CAAC;AAEL,CAAC,EACD;EACEN,IAAI,EAAE,SAAS;EAAE;EACjBC,SAAS,EAAEH,MAAM;EACjBY,QAAQ,EAAE,CACR;IACEV,IAAI,EAAE,KAAK;IACXO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oBAAoB;MAAA;IAAA;EAC9C,CAAC,EACD;IACEN,IAAI,EAAE,OAAO;IACbO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,sBAAsB;MAAA;IAAA;EAChD,CAAC,EACD;IACEN,IAAI,EAAE,OAAO;IACbO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,sBAAsB;MAAA;IAAA;EAChD,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qBAAqB;MAAA;IAAA;EAC/C,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qBAAqB;MAAA;IAAA;EAC/C,CAAC,EACD;IACEN,IAAI,EAAE,OAAO;IACbO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAS,CAAC;IACxBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,sBAAsB;MAAA;IAAA;EAChD,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qBAAqB;MAAA;IAAA;EAC/C,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,yBAAyB;MAAA;IAAA;EACnD,CAAC;AAEL,CAAC,EACD;EACEN,IAAI,EAAE,KAAK;EAAE;EACbC,SAAS,EAAEH,MAAM;EACjBY,QAAQ,EAAE,CACR;IACEV,IAAI,EAAE,IAAI;IACVO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,eAAe;MAAA;IAAA;EACzC,CAAC,EACD;IACEN,IAAI,EAAE,QAAQ;IACdO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mBAAmB;MAAA;IAAA;EAC7C,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qBAAqB;MAAA;IAAA;EAC/C,CAAC,EACD;IACEN,IAAI,EAAE,KAAK;IACXO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,gBAAgB;MAAA;IAAA;EAC1C,CAAC;AAEL,CAAC,EACD;EACEN,IAAI,EAAE,KAAK;EAAE;EACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,YAAY;IAAA;EAAA,CAAC;EACrCG,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,CACR;IACEC,IAAI,EAAE,QAAQ;IACdX,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,iBAAiB;MAAA;IAAA;EAC3C,CAAC,EACD;IACEK,IAAI,EAAE,SAAS;IACfX,IAAI,EAAE,OAAO;IACbO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC,EACD;IACEK,IAAI,EAAE,SAAS;IACfX,IAAI,EAAE,QAAQ;IACdO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mBAAmB;MAAA;IAAA;EAC7C,CAAC,EACD;IACEK,IAAI,EAAE,SAAS;IACfX,IAAI,EAAE,OAAO;IACbO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC,EACD;IACEK,IAAI,EAAE,UAAU;IAChBX,IAAI,EAAE,QAAQ;IACdO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mBAAmB;MAAA;IAAA;EAC7C,CAAC,EACD;IACEK,IAAI,EAAE,MAAM;IACZX,IAAI,EAAE,IAAI;IACVO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,eAAe;MAAA;IAAA;EACzC,CAAC,EACD;IACEK,IAAI,EAAE,WAAW;IACjBX,IAAI,EAAE,SAAS;IACfO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oBAAoB;MAAA;IAAA;EAC9C,CAAC;AAEL,CAAC,EACD;EACEN,IAAI,EAAE,KAAK;EAAE;EACbC,SAAS,EAAEH,MAAM;EACjBY,QAAQ,EAAE,CACR;IACEV,IAAI,EAAE,QAAQ;IACdO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mBAAmB;MAAA;IAAA;EAC7C,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,iBAAiB;MAAA;IAAA;EAC3C,CAAC;AAEL,CAAC,EACD;EACEN,IAAI,EAAE,KAAK;EAAE;EACbC,SAAS,EAAEH,MAAM;EACjBY,QAAQ,EAAE,CACR;IACEV,IAAI,EAAE,YAAY;IAClBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAS,CAAC;IACxBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,uBAAuB;MAAA;IAAA;EACjD,CAAC,EACD;IACEN,IAAI,EAAE,WAAW;IACjBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAU,CAAC;IACzBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,sBAAsB;MAAA;IAAA;EAChD,CAAC,EACD;IACEN,IAAI,EAAE,aAAa;IACnBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAS,CAAC;IACxBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,wBAAwB;MAAA;IAAA;EAClD,CAAC,EACD;IACEN,IAAI,EAAE,QAAQ;IACdO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mBAAmB;MAAA;IAAA;EAC7C,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAY,CAAC;IAC3BV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qBAAqB;MAAA;IAAA;EAC/C,CAAC,EACD;IACEN,IAAI,EAAE,YAAY;IAClBO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAS,CAAC;IACxBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,uBAAuB;MAAA;IAAA;EACjD,CAAC;AAEL,CAAC,EACD;EACEN,IAAI,EAAE,KAAK;EAAE;EACbC,SAAS,EAAEH,MAAM;EACjBY,QAAQ,EAAE,CACR;IACEV,IAAI,EAAE,OAAO;IACbO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfO,IAAI,EAAE;MAAEI,IAAI,EAAE;IAAO,CAAC;IACtBV,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oBAAoB;MAAA;IAAA;EAC9C,CAAC;AAEL,CAAC;AACD;AACA;EAAEN,IAAI,EAAE,GAAG;EAAES,QAAQ,EAAE,MAAM;EAAEG,MAAM,EAAE;AAAK,CAAC,CAC9C;AAED,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA;EAAA,OAAS,IAAIjB,MAAM,CAAC;IACpC;IACAkB,cAAc,EAAE,SAAhBA,cAAcA,CAAA;MAAA,OAAS;QAAEC,CAAC,EAAE;MAAE,CAAC;IAAA,CAAC;IAChCC,MAAM,EAAEjB;EACV,CAAC,CAAC;AAAA;AAEF,IAAMkB,MAAM,GAAGJ,YAAY,CAAC,CAAC;AAE7B,OAAO,SAASK,WAAWA,CAAA,EAAG;EAC5B,IAAMC,SAAS,GAAGN,YAAY,CAAC,CAAC;EAChCI,MAAM,CAACG,OAAO,GAAGD,SAAS,CAACC,OAAO,EAAC;AACrC;AAEA,eAAeH,MAAM", "ignoreList": []}]}