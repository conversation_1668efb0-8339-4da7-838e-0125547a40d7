{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\back\\backView.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\back\\backView.vue", "mtime": 1753320296353}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEFzc2V0U3RhdHVzVHlwZSBhcyBfZ2V0QXNzZXRTdGF0dXNUeXBlLCBnZXRBc3NldFN0YXR1c1RleHQgYXMgX2dldEFzc2V0U3RhdHVzVGV4dCB9IGZyb20gJy4uL2pzL2Fzc2V0LmpzJzsKaW1wb3J0IEFzc2V0VmlldyBmcm9tICcuLi9hY2NvdW50L0RldGFpbFZpZXcudnVlJzsKaW1wb3J0IFVwbG9hZEZpbGUgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1VwbG9hZEZpbGUudnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIEFzc2V0VmlldzogQXNzZXRWaWV3LAogICAgVXBsb2FkRmlsZTogVXBsb2FkRmlsZQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHZpc2libGU6IGZhbHNlLAogICAgICBmb3JtOiB7fSwKICAgICAgcmVnaW9uVGV4dDogJycsCiAgICAgIGFzc2V0TGlzdDogW10sCiAgICAgIGZpbGVMaXN0OiBbXSwKICAgICAgYXR0YWNoQ29udGV4dDogdGhpcy4kc3RvcmUuZ2V0dGVycy5hdHRhY2hDb250ZXh0CiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0QXNzZXRTdGF0dXNUeXBlOiBmdW5jdGlvbiBnZXRBc3NldFN0YXR1c1R5cGUodikgewogICAgICByZXR1cm4gX2dldEFzc2V0U3RhdHVzVHlwZSh2LnN0YXR1cyk7CiAgICB9LAogICAgZ2V0QXNzZXRTdGF0dXNUZXh0OiBmdW5jdGlvbiBnZXRBc3NldFN0YXR1c1RleHQodikgewogICAgICByZXR1cm4gX2dldEFzc2V0U3RhdHVzVGV4dCh2LnN0YXR1cyk7CiAgICB9LAogICAgc2hvdzogZnVuY3Rpb24gc2hvdyhpdGVtKSB7CiAgICAgIHRoaXMudmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMucmVnaW9uVGV4dCA9IGl0ZW0uZGVwdE5hbWUgJiYgaXRlbS5yZWdpb25OYW1lID8gIiIuY29uY2F0KGl0ZW0uZGVwdE5hbWUsICIvIikuY29uY2F0KGl0ZW0ucmVnaW9uTmFtZSkgOiBpdGVtLmRlcHROYW1lIHx8IGl0ZW0ucmVnaW9uTmFtZSB8fCAnJzsKICAgICAgdGhpcy5mb3JtID0gaXRlbTsKICAgICAgdGhpcy5hc3NldExpc3QgPSBpdGVtLmRldGFpbHMgfHwgW107CiAgICAgIHRoaXMuZmlsZUxpc3QgPSBpdGVtLmZpbGVMaXN0IHx8IFtdOwogICAgfSwKICAgIHZpZXdBc3NldDogZnVuY3Rpb24gdmlld0Fzc2V0KGlkKSB7CiAgICAgIHRoaXMuJHJlZnMuYXNzZXRWaWV3LnNob3coaWQpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["getAssetStatusType", "getAssetStatusText", "Asset<PERSON>iew", "UploadFile", "components", "data", "visible", "form", "regionText", "assetList", "fileList", "attachContext", "$store", "getters", "methods", "v", "status", "show", "item", "deptName", "regionName", "concat", "details", "viewAsset", "id", "$refs", "assetView"], "sources": ["src/views/asset/back/backView.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog v-dialog-drag title=\"退库申请信息\" width=\"900px\" :visible.sync=\"visible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"dataform\" size=\"small\" label-width=\"140px\" :model=\"form\">\r\n        <el-form-item label=\"实际退库日期：\">\r\n          <el-input v-model=\"form.time\" readonly class=\"form-static\" style=\"width:100%\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"使用区域/地点：\">\r\n          <el-input v-model=\"regionText\" readonly class=\"form-static\" style=\"width:100%\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"领用说明：\">\r\n          <el-input v-model=\"form.memo\" type=\"textarea\" readonly class=\"form-static\" style=\"width:100%\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <upload-file v-if=\"fileList.length\" v-model=\"fileList\" multiple disabled />\r\n      <div style=\"margin-top: 10px;\">退库资产明细：</div>\r\n      <el-divider></el-divider>\r\n      <el-table :data=\"assetList\" size=\"small\" border>\r\n        <el-table-column label=\"资产类型\" prop=\"typeName\" width=\"180\" header-align=\"center\" />\r\n        <el-table-column label=\"资产编码\" prop=\"no\" width=\"130\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"danger\" size=\"mini\" @click.stop=\"viewAsset(scope.row.id)\">{{ scope.row.no }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产名称\" prop=\"name\" min-width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"primary\" size=\"mini\" @click.stop=\"viewAsset(scope.row.id)\">{{ scope.row.name }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\" header-align=\"center\" />\r\n        <el-table-column label=\"当前状态\" prop=\"status\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getAssetStatusType(scope.row)\" size=\"small\">{{ getAssetStatusText(scope.row) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"visible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <asset-view ref=\"assetView\" />\r\n  </div>\r\n</template>\r\n<script>\r\n\r\nimport { getAssetStatusType, getAssetStatusText } from '../js/asset.js'\r\n\r\nimport AssetView from '../account/DetailView.vue'\r\nimport UploadFile from '@/views/components/UploadFile.vue'\r\n\r\nexport default {\r\n  components: { AssetView, UploadFile },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      form: { },\r\n      regionText: '',\r\n      assetList: [],\r\n      fileList: [],\r\n      attachContext: this.$store.getters.attachContext\r\n    }\r\n  },\r\n  methods: {\r\n    getAssetStatusType(v) {\r\n      return getAssetStatusType(v.status)\r\n    },\r\n    getAssetStatusText(v) {\r\n      return getAssetStatusText(v.status)\r\n    },\r\n    show(item) {\r\n      this.visible = true\r\n      this.regionText = item.deptName && item.regionName ? `${item.deptName}/${item.regionName}` : (item.deptName || item.regionName || '');\r\n      this.form = item\r\n      this.assetList = item.details || []\r\n      this.fileList = item.fileList || []\r\n    },\r\n    viewAsset(id) {\r\n      this.$refs.assetView.show(id)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,SAAAA,kBAAA,IAAAA,mBAAA,EAAAC,kBAAA,IAAAA,mBAAA;AAEA,OAAAC,SAAA;AACA,OAAAC,UAAA;AAEA;EACAC,UAAA;IAAAF,SAAA,EAAAA,SAAA;IAAAC,UAAA,EAAAA;EAAA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA;MACAC,aAAA,OAAAC,MAAA,CAAAC,OAAA,CAAAF;IACA;EACA;EACAG,OAAA;IACAd,kBAAA,WAAAA,mBAAAe,CAAA;MACA,OAAAf,mBAAA,CAAAe,CAAA,CAAAC,MAAA;IACA;IACAf,kBAAA,WAAAA,mBAAAc,CAAA;MACA,OAAAd,mBAAA,CAAAc,CAAA,CAAAC,MAAA;IACA;IACAC,IAAA,WAAAA,KAAAC,IAAA;MACA,KAAAZ,OAAA;MACA,KAAAE,UAAA,GAAAU,IAAA,CAAAC,QAAA,IAAAD,IAAA,CAAAE,UAAA,MAAAC,MAAA,CAAAH,IAAA,CAAAC,QAAA,OAAAE,MAAA,CAAAH,IAAA,CAAAE,UAAA,IAAAF,IAAA,CAAAC,QAAA,IAAAD,IAAA,CAAAE,UAAA;MACA,KAAAb,IAAA,GAAAW,IAAA;MACA,KAAAT,SAAA,GAAAS,IAAA,CAAAI,OAAA;MACA,KAAAZ,QAAA,GAAAQ,IAAA,CAAAR,QAAA;IACA;IACAa,SAAA,WAAAA,UAAAC,EAAA;MACA,KAAAC,KAAA,CAAAC,SAAA,CAAAT,IAAA,CAAAO,EAAA;IACA;EACA;AACA", "ignoreList": []}]}