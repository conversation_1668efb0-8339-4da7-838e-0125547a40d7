import Vue from 'vue'
import store from '@/store'

function $_priv(code) {
  var user = store.getters.user
  if (!user) return false
  if (user.isSuperAdmin) return true
  if (!user.opts) return false
  for (var i = 0; i < user.opts.length; i++) {
    if (user.opts[i] === code) return true
  }
  return false
}

// 操作权限
Vue.directive('priv', {
  bind: function (el, binding, vnode) {
    if (!$_priv(binding.value)) el.style.display = 'none'
  },
  inserted: function (el, binding, vnode, oldVnode) {
  },
  updated: function (el, binding, vnode, oldVnode) {
  }
})

Vue.prototype.$priv = $_priv

// 省中心权限
Vue.directive('root-dept', {
  bind: function (el, binding, vnode) {
    var user = store.getters.user
    if (!user || !(user.isSuperAdmin || user.deptType === '1' && user.parentDept === '0')) el.style.display = 'none'
  },
  inserted: function (el, binding, vnode, oldVnode) {
  },
  updated: function (el, binding, vnode, oldVnode) {
  }
})

function checkMenu(menus, code) {
  if (menus && menus.length) {
    const menu = menus.find(r => r.code === code)
    if (menu) return true
    return checkMenu(menu.children, code)
  }
  return false
}

// 菜单权限
function $_menu_priv(code) {
  var user = store.getters.user
  if (!user) return false
  if (user.isSuperAdmin) return true
  return checkMenu(user.menuCodes)
}

// 菜单权限
Vue.directive('menu-priv', {
  bind: function (el, binding, vnode) {
    if (!$_menu_priv(binding.value)) el.style.display = 'none'
  },
  inserted: function (el, binding, vnode, oldVnode) {
  },
  updated: function (el, binding, vnode, oldVnode) {
  }
})

Vue.prototype.$_menu_priv = $_menu_priv
