package com.zy.app.svc;

import com.zy.app.dao.AppDAO;
import com.zy.app.vo.Menu;
import com.zy.app.vo.User;
import com.zy.core.EX;
import com.zy.core.IApp;
import com.zy.dam.asset.dao.AmAssetDAO;
import com.zy.dam.feign.AmapGisFeign;
import com.zy.model.BaseUser;
import com.zy.model.Result;
import com.zy.model.TreeNode;
import com.zy.model.TypeKeyValue;
import com.zy.sys.dao.SysAccessDAO;
import com.zy.util.CryptUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class AppSVC implements IApp {

    @Resource
    private AppDAO dao;

    @Resource
    private SysAccessDAO accessDAO;

    @Resource
    private AmapGisFeign amapGisFeign;

    @Resource
    private AmAssetDAO amAssetDAO;

    @Value("${zy.super-admin-name}")
    private String superAdminAccount;

    @Override
    public Class<? extends BaseUser> getUserClass() {
        return User.class;
    }

    @Override
    public boolean checkAuth(BaseUser user, String api) {
        return false;
    }

    @Override
    public boolean beforeAccess(BaseUser user, String uri, String method, String ip, String uuid) {
        if (uri.indexOf("heartbeat") != -1 || uri.toLowerCase().indexOf("upload") != -1) return true;
        accessDAO.insert(uri, ip, user.getId(), "");
        return false;
    }

    @Override
    public boolean afterAccess(String uuid) {
        return false;
    }

    public void loginSuccess(User user) {

    }

    /**
     * 获取登录用户
     *
     * @param account  帐号
     * @param password 密码
     * @return 用户信息
     */
    public User findForLogin(String account, String password) {
        if (account == null || password == null) return null;
        User user = dao.findLogin(account, CryptUtils.md5(password));
        if (user == null) return null;
        if (!"1".equals(user.getStatus())) {
            throw new EX("帐号已经被禁用");
        }
        // 加载下级机构ID列表
        user.setDeptIds(TreeNode.toPlainIdByList(dao.findDeptIdTree(), user.getDept(), true, null));
        return user;
    }

    /**
     * 附加信息
     *
     * @param user 用户
     * @return User 用户信息
     */
    public User append(User user) {
        // 加载用户菜单
        loadMenu(user);
        // 加载权限按钮
        user.setOpts(dao.findOpt(user.getId()));
        //加载字典
        user.setDictCodes(findAllCode());
        return user;
    }

    /**
     * 更新密码
     *
     * @param id          ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 结果
     */
    public Result updatePassword(String id, String oldPassword, String newPassword) {
        String password = dao.findPassword(id);
        if (password == null) return Result.err("无法获取用户信息");
        if (!CryptUtils.verifyMd5(password, oldPassword)) return Result.err("原密码不正确");
        dao.updatePassword(id, CryptUtils.md5(newPassword));
        return Result.ok();
    }

    /**
     * 加载用户菜单
     *
     * @param user 用户
     */
    public void loadMenu(User user) {
        if (user == null || user.getId() == null) return;
        List<Menu> menus = superAdminAccount.equals(user.getAccount()) ? dao.findAllMenu() : dao.findUserMenu(user.getId());
        List<Menu> roots = new ArrayList<>();
        Map<String, Menu> map = new HashMap<>();
        for (Menu menu : menus) { //遍历并初始化根级
            map.put(menu.getCode(), menu);
            if ("0".equals(menu.getPcode()) || "00".equals(menu.getPcode())) {
                roots.add(menu);
            }
        }
        Menu cm;
        for (Menu menu : menus) { //遍历调整上下级关系
            if ("0".equals(menu.getPcode()) || "00".equals(menu.getPcode())) continue;
            cm = map.get(menu.getPcode());
            if (cm != null) cm.addChild(menu);
        }
        //移除空目录
        removeEmptyNode(roots);
        user.setMenus(roots);
    }

    /**
     * 移除空目录
     *
     * @param nodes 处理节点
     */
    private void removeEmptyNode(List<Menu> nodes) {
        if (nodes == null || nodes.isEmpty()) return;
        Menu node;
        Iterator<Menu> it = nodes.iterator();
        while (it.hasNext()) {
            node = it.next();
            removeEmptyNode(node.getChildren()); //递归算法
            if (node.isEmpty() && (node.getPath() == null || node.getPath().isEmpty() || "1".equals(node.getType()))) { //没有子节点，并且不是菜单项则移除
                it.remove();
            }
        }
    }

    /**
     * 获取所有字典编码
     *
     * @return List
     */
    public List<TypeKeyValue> findAllCode() {
        return dao.findAllCode();
    }

    /**
     * 获取超级管理员账号名
     * @return 超级管理员账号名
     */
    public String getSuperAdminAccount() {
        return superAdminAccount;
    }

}
