{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\components\\PageTable.vue?vue&type=style&index=0&id=10a976c0&lang=scss&scoped=true", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\components\\PageTable.vue", "mtime": 1753320296361}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1747730939289}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1747730939045}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1747730939142}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1747730941929}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5wYWdlLXRhYmxlLWN0biB7DQogID4gLmVsLXRhYmxlIHsNCiAgICB3aWR0aDogJzEwMCUnOw0KICAgIG1hcmdpbi1ib3R0b206IDE0cHg7DQogICAgYm9yZGVyOiAxcHggc29saWQgI2ViZWVmNTsNCiAgICBib3JkZXItYm90dG9tOiB1bnNldDsNCiAgfQ0KICA+IC5mb290ZXIgew0KICAgIGhlaWdodDogNDBweCAhaW1wb3J0YW50Ow0KICAgIC5zaXplLWluZm8gew0KICAgICAgZGlzcGxheTogaW5saW5lOw0KICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgY29sb3I6ICM2NjY2NjY7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["PageTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PageTable.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\r\n  <el-container class=\"page-table-ctn\">\r\n    <!-- <el-popover v-if=\"columns.length > 0\" width=\"60\" trigger=\"click\">\r\n      <el-checkbox-group v-model=\"columns\">\r\n        <el-checkbox v-for=\"item in columns\" :key=\"item.index\" :label=\"item.title\" />\r\n      </el-checkbox-group>\r\n      <el-button slot=\"reference\" icon=\"el-icon-more\" circle style=\"margin-left: 100px;\"></el-button>\r\n    </el-popover> -->\r\n    <el-table ref=\"grid\" v-loading=\"loading\" :max-height=\"maxHeight\" :data=\"rows\" v-bind=\"$attrs\" v-on=\"$listeners\">\r\n      <slot></slot>\r\n    </el-table>\r\n    <el-footer v-if=\"paging\" class=\"footer\">\r\n      <div class=\"size-info\">\r\n        <span v-if=\"total > 1\">显示第 {{ from }} 条到第 {{ to }} 条的数据，</span> 共{{ total }} 条数据\r\n      </div>\r\n      <el-pagination\r\n        style=\"float:right\"\r\n        :layout=\"layout\"\r\n        :page-sizes=\"pageSizes\"\r\n        :current-page=\"pi\"\r\n        :page-size=\"pz\"\r\n        :total=\"total\"\r\n        v-bind=\"$attrs\"\r\n        @current-change=\"handleNumberChange\"\r\n        @size-change=\"handleSizeChange\"\r\n      ></el-pagination>\r\n    </el-footer>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\n\r\nimport request from '../../utils/request'\r\n\r\nexport default {\r\n  name: 'PageTable',\r\n  props: {\r\n    path: {\r\n      type: String,\r\n      require: true,\r\n      default: null\r\n    },\r\n    pageSize: {\r\n      type: Number,\r\n      default: 10\r\n    },\r\n    pageSizes: {\r\n      type: Array,\r\n      default: () => [10, 20, 30, 50, 100]\r\n    },\r\n    layout: {\r\n      type: String,\r\n      default: 'sizes, prev, pager, next, jumper'\r\n    },\r\n    paging: { // 是否分页，默认为true，即分页。（不分页时将每页条数设置最大。）\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    query: { // 初始化参数\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    auto: { // 自动查询\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    checkField: {\r\n      type: String,\r\n      default: null\r\n    }\r\n    // columns: {\r\n    //   type: Array,\r\n    //   default: () => []\r\n    // }\r\n  },\r\n  data() {\r\n    return {\r\n      pi: 1, // 页标\r\n      pz: this.pageSize, // 页长\r\n      params: this.query || {},\r\n      rows: [],\r\n      total: 0,\r\n      from: 0,\r\n      to: 0,\r\n      maxHeight: null,\r\n      loading: false\r\n    }\r\n  },\r\n  watch: {\r\n    // columnSelecteds(newArrayVal) {\r\n    //   console.log(newArrayVal)\r\n    //   // 计算为被选中的列标题数组\r\n    //   var nonSelecteds = this.columns.filter(item => newArrayVal.indexOf(item.index) === -1).map(item => item.index)\r\n    //   this.columns.filter(item => {\r\n    //     const isNonSelected = nonSelecteds.indexOf(item.index) !== -1\r\n    //     if (isNonSelected) {\r\n    //       // 隐藏未选中的列\r\n    //       item.visible = false\r\n    //       this.$nextTick(() => {\r\n    //         this.$refs.grid.doLayout()\r\n    //       })\r\n    //     } else {\r\n    //       // 显示已选中的列\r\n    //       item.visible = true\r\n    //       this.$nextTick(() => {\r\n    //         this.$refs.grid.doLayout()\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // }\r\n  },\r\n  mounted() {\r\n    if (this.auto) this.search()\r\n  },\r\n  methods: {\r\n    setParams(value) {\r\n      this.params = value || {}\r\n    },\r\n    setMaxHeight(value) {\r\n      this.maxHeight = value\r\n      this.$refs.grid.doLayout()\r\n    },\r\n    handleSizeChange(value) {\r\n      this.pz = value\r\n      this.search(1)\r\n    },\r\n    handleNumberChange(value) {\r\n      this.search(value)\r\n    },\r\n    search(arg, a) {\r\n      if (!this.path) return\r\n      const ps = { pageNumber: 1 }\r\n      const argType = typeof arg\r\n      if (argType === 'undefined') ps.pageNumber = 1\r\n      else if (argType === 'number') ps.pageNumber = arg\r\n      else if (argType === 'object') {\r\n        this.params = arg\r\n        if (typeof a === 'number') ps.pageNumber = a // 指定页码\r\n        if (typeof a === 'boolean') this.empty() // 查询前清空\r\n      } else ps.pageNumber = arg.pageNumber\r\n      this.pi = ps.pageNumber\r\n      if (this.paging) {\r\n        this.params.pageNumber = ps.pageNumber\r\n        this.params.pageSize = this.pz\r\n      }\r\n      this.loading = true\r\n      request({\r\n        url: this.path,\r\n        data: this.params\r\n      }).then(res => {\r\n        this.loading = false\r\n        if (this.paging) this.renderPage(res)\r\n        else this.renderList(res.rows ? res.rows : res)\r\n        this.$emit('loaded', res) // 加载数据返回\r\n      }).catch(err => {\r\n        this.loading = false\r\n        console.log(err)\r\n      })\r\n    },\r\n    empty() {\r\n      this.pi = 1\r\n      this.rows = []\r\n      this.total = 0\r\n      this.from = 0\r\n      this.to = 0\r\n    },\r\n    renderList(res) {\r\n      this.rows = res\r\n    },\r\n    renderPage(res) {\r\n      if (this.checkField) res.rows.forEach(r => { r[this.checkField] = false })\r\n      this.rows = res.rows\r\n      this.total = res.total\r\n      if (this.total > 0) {\r\n        this.from = (this.pi - 1) * this.pz + 1\r\n        this.to = this.from + (this.rows && this.rows.length ? this.rows.length - 1 : 0)\r\n      } else {\r\n        this.from = 0\r\n        this.to = 0\r\n      }\r\n    },\r\n    getData() {\r\n      return this.rows\r\n    },\r\n    getSelection() {\r\n      return this.$refs.grid.selection\r\n    },\r\n    getSelectionId(field) {\r\n      const items = this.$refs.grid.selection\r\n      if (!field) field = 'id'\r\n      const ids = []\r\n      for (let i = 0; i < items.length; i++) {\r\n        if (items[i][field]) ids.push(items[i][field])\r\n      }\r\n      return ids\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-table-ctn {\r\n  > .el-table {\r\n    width: '100%';\r\n    margin-bottom: 14px;\r\n    border: 1px solid #ebeef5;\r\n    border-bottom: unset;\r\n  }\r\n  > .footer {\r\n    height: 40px !important;\r\n    .size-info {\r\n      display: inline;\r\n      font-size: 12px;\r\n      color: #666666;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}