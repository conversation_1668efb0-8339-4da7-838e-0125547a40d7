import Vue from 'vue'
import store from '@/store'

function $_part(code) {
  const user = store.getters.user
  if (!user) return false
  if (user.isSuperAdmin) return true
  if (!user.deptType) return false
  if (code.indexOf(user.deptType) >= 0) return true
  return false
}

// 操作权限
Vue.directive('part', {
  bind: function (el, binding, vnode) {

  },
  inserted: function (el, binding, vnode, oldVnode) {
    // if (!$_part(binding.value)) el.parentNode.removeChild(el)
    // if (!$_part(binding.value)) el.remove()
    if (!$_part(binding.value)) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  },
  updated: function (el, binding, vnode, oldVnode) {
    // console.log(el, binding)

    // if (!$_part(binding.value)) el.remove()
  }
})

Vue.prototype.$part = $_part
