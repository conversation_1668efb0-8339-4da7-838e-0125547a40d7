<template>
  <div class="app-container">
    <div class="filter">
      <el-form :model="qform" label-width="100px" @submit.native.prevent="search">
        <el-row :gutter="10">
          <el-col :span="10">
            <el-form-item v-if="deptVisible" label="中心：">
              <center-dept-tree-box v-model="qform.dept" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="月份：">
              <el-date-picker v-model="qform.month" type="month" value-format="yyyyMM" placeholder="选择月" style="width:130px;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button class="filter-item" type="primary" icon="el-icon-search" @click="search">查询</el-button>
            <el-button v-loading.fullscreen.lock="fullscreenLoading" class="filter-item" type="success" icon="el-icon-plus" @click="addBill">新增</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <page-table ref="grid" v-table-height path="/rp/zdj/zbt/page" :query="qform" stripe border size="mini">
      <el-table-column type="index" width="50" />
      <el-table-column label="部门" prop="deptName" width="100" />
      <el-table-column label="月份" prop="month" width="90" align="center" :formatter="colMonth" />
      <el-table-column label="生成时间" prop="buildTime" width="140" align="center" />
      <el-table-column label="操作人" prop="buildUserName" width="80" align="center" />
      <el-table-column label="核定时间" prop="checkTime" width="140" align="center" />
      <el-table-column label="核定人" prop="checkUserName" width="80" align="center" />
      <el-table-column label="备注" prop="memo" />
      <el-table-column label="操作" width="220">
        <template slot-scope="scope">
          <el-button v-loading.fullscreen.lock="fullscreenLoading" size="mini" type="primary" @click.stop="viewBill(scope.row)">查看</el-button>
          <template v-if="scope.row.status === '1'">
            <el-button v-loading.fullscreen.lock="fullscreenLoading" size="mini" type="primary" @click.stop="editBill(scope.row)">编辑</el-button>
            <el-button v-loading.fullscreen.lock="fullscreenLoading" size="mini" type="success" @click.stop="checkBill(scope.row)">核定</el-button>
          </template>
          <template v-else>
            <el-button v-loading.fullscreen.lock="fullscreenLoading" size="mini" type="success" @click.stop="exportBill(scope.row)">导出</el-button>
          </template>
          <el-button size="mini" type="danger" @click.stop="removeBill(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </page-table>
    <biz-bill-create ref="billCreate" @success="search" />
    <biz-bill-edit ref="billEdit" @success="search" />
    <biz-bill-check ref="billCheck" @success="search" />
    <biz-bill-view ref="billView" />
  </div>
</template>

<script>
import PageTable from '@/views/components/PageTable.vue'
import CenterDeptTreeBox from '@/views/components/CenterDeptTreeBox.vue'
import BizBillCreate from './BizBillCreate.vue'
import BizBillEdit from './BizBillEdit.vue'
import BizBillCheck from './BizBillCheck.vue'
import BizBillView from './BizBillView.vue'
import { Loading } from 'element-ui'

export default {
  components: { PageTable, CenterDeptTreeBox, BizBillCreate, BizBillEdit, BizBillCheck, BizBillView },
  data() {
    return {
      formLabelWidth: '100px',
      fullscreenLoading: false,
      deptVisible: false,
      qform: {
        dept: null,
        month: null
      }
    }
  },
  mounted() {
    const user = this.$store.getters.user
    this.deptVisible = user.isSuperAdmin || user.parentDept === '0'
    this.search()
  },
  methods: {
    colMonth(r, c, v) {
      return v.substring(0, 4) + '年' + v.substring(4) + '月'
    },
    search() {
      this.$refs.grid.search(this.qform)
    },
    addBill() {
      this.$refs.billCreate.show({})
    },
    editBill(data) {
      this.fullscreenLoading = true
      this.$http('/rp/zdj/zbt/get/' + data.id).then(res => {
        this.fullscreenLoading = false
        if (res.code > 0) {
          this.$refs.billEdit.show(res.data)
        }
      }).catch(() => {
        this.fullscreenLoading = false
      })
    },
    checkBill(data) {
      this.fullscreenLoading = true
      this.$http('/rp/zdj/zbt/get/' + data.id).then(res => {
        this.fullscreenLoading = false
        if (res.code > 0) {
          this.$refs.billCheck.show(res.data)
        }
      }).catch(() => {
        this.fullscreenLoading = false
      })
    },
    viewBill(data) {
      this.fullscreenLoading = true
      this.$http('/rp/zdj/zbt/get/' + data.id).then(res => {
        this.fullscreenLoading = false
        if (res.code > 0) {
          this.$refs.billView.show(res.data)
        }
      }).catch(() => {
        this.fullscreenLoading = false
      })
    },
    exportBill(data) {
      const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })
      this.$jasper({ url: '/rp/zdj/zbt/export/' + data.id, responseType: 'blob' }).then(blob => {
        loadInst.close()
        this.$saveAs(blob, data.deptName + '_' + data.month + '.xlsx')
      }).catch(err => {
        loadInst.close()
        this.$message.error('导出报表生成出错:' + err)
      })
    },
    removeBill(data) {
      this.$confirm('此操作将永久删除该报表, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        this.$http('/rp/zdj/zbt/delete/' + data.id).then(res => {
          if (res.code > 0) {
            this.$message.success('删除成功')
            this.detailVisible = false
            this.search()
          }
        })
      }).catch(() => {})
    }
  }
}
</script>
