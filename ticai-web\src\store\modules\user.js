import { removeToken, setToken } from '@/utils/auth'
import request from '@/utils/request'

const getDefaultState = () => {
  return {
    attachContext: '/attach', // 附件上下文
    token: '', // 登录令牌
    info: {}, // 用户信息
    menus: [], // 菜单
    dict: {}, // 字典
    systemConfig: {} // 系统配置
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_USER: (state, user) => {
    if (user.attachContext) state.attachContext = user.attachContext
    state.token = user.token
    state.info = user
    state.menus = user.menus
    var dict = {}
    if (user.dictCodes) {
      var codes
      user.dictCodes.forEach(r => {
        codes = dict[r.type]
        if (!codes) dict[r.type] = codes = []
        codes.push({ value: r.key, text: r.value })
      })
    }
    state.dict = dict
  },
  SET_SYSTEM_CONFIG: (state, config) => {
    state.systemConfig = config
  }
}

const actions = {
  // user login
  login({ commit }, data) {
    return new Promise((resolve, reject) => {
      request({ url: '/login', method: 'post', data: data }).then(res => {
        if (res.code > 0 && res.data) {
          commit('SET_USER', res.data)
          setToken(res.data.token)
          resolve(res)
        } else reject('登录失败')
      }).catch(() => {
        reject('登录异常')
      })
    })
  },
  // user logout
  logout({ commit }) {
    return new Promise((resolve, reject) => {
      request('/logout').then(r => {
        // resetRouter()
        commit('RESET_STATE')
        removeToken()
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  get({ commit }, token) {
    return new Promise((resolve, reject) => {
      // 静默不需要错误提示
      request({ url: '/user', silent: true }).then(r => {
        if (r.code > 0 && r.data) {
          commit('SET_USER', r.data)
          setToken(r.data.token)
          resolve(r)
        } else reject('无法获取用户')
      }).catch(error => {
        reject(error)
      })
    })
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('RESET_STATE')
      removeToken()
      resolve()
    })
  },
  // load system config
  loadSystemConfig({ commit }) {
    return new Promise((resolve, reject) => {
      request({ url: '/app/config', silent: true }).then(r => {
        if (r.code > 0 && r.data) {
          commit('SET_SYSTEM_CONFIG', r.data)
          resolve(r.data)
        } else reject('无法获取系统配置')
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

