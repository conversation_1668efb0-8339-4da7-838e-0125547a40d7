<template>
  <div class="app-container">
    <div class="filter">
      <el-form :model="qform" inline @submit.native.prevent="search">
        <div class="filter-item">
          <label>帐号：</label>
          <div>
            <el-input v-model="qform.account" clearable autocomplete="off" style="width:150px;" />
          </div>
        </div>
        <div class="filter-item">
          <label>姓名：</label>
          <div>
            <el-input v-model="qform.name" clearable autocomplete="off" style="width:150px;" />
          </div>
        </div>
        <div class="filter-item">
          <label>所属单位：</label>
          <div>
            <tree-box v-model="qform.dept" :data="deptTree" expand-all clearable />
          </div>
        </div>
        <div class="filter-item">
          <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
          <el-button type="success" icon="el-icon-plus" @click="add">新增</el-button>
        </div>
      </el-form>
    </div>
    <page-table ref="grid" v-table-height path="/sys/user/page" size="mini" stripe border>
      <el-table-column type="index" width="50" />
      <el-table-column label="帐号" prop="account" width="120" />
      <el-table-column label="姓名" prop="name" width="150" />
      <el-table-column label="性别" prop="gender" width="50" align="center" :formatter="colGender" />
      <el-table-column label="状态" prop="status" width="60" align="center" :formatter="fnStatus" />
      <el-table-column label="所属机构" prop="deptName" width="100" />
      <el-table-column label="用户类型" prop="type" width="70" align="center" :formatter="fnType" />
      <el-table-column label="角色" prop="roleNames" />
      <el-table-column label="操作" width="250" align="center">
        <template slot-scope="scope">
          <div v-if="!isSuperAdminAccount(scope.row)">
            <el-button type="primary" size="mini" @click.stop="detail(scope.row)">编辑</el-button>
            <el-button type="success" size="mini" @click.stop="assignRole(scope.row)">角色</el-button>
            <el-button type="danger" size="mini" @click.stop="remove(scope.row)">删除</el-button>
            <el-button type="warning" size="mini" @click.stop="resetPassword(scope.row)">重置密码</el-button>
          </div>
        </template>
      </el-table-column>
    </page-table>
    <el-dialog v-dialog-drag title="用户信息" :visible.sync="detailVisible" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form ref="dataform" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属机构：" prop="dept">
              <el-input v-if="/2|3/.test(form.type)" v-model="form.deptName" readonly class="readonly" />
              <tree-box v-else v-model="form.dept" :data="deptTree" expand-all :clearable="false" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名：" prop="name">
              <el-input v-model="form.name" maxlength="32" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别：" prop="gender">
              <el-radio-group v-model="form.gender">
                <el-radio label="1">男</el-radio>
                <el-radio label="2">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="帐号：" prop="account">
              <el-input v-model="form.account" :readonly="opt == 'update'" maxlength="32" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <span class="form-memo">{{ opt == 'add' ? '初始化密码为：Hntc@1234' : '帐号不能修改' }}</span>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编码：" prop="no">
              <el-input v-model="form.no" maxlength="12" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式：" prop="phone">
              <el-input v-model="form.phone" maxlength="32" autocomplete="off" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <role ref="role" @success="search" />
    <el-dialog v-dialog-drag title="重置密码" :visible.sync="passwordVisible" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form ref="passwordform" :model="passwordform" label-width="110px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="姓名：" prop="name">
              <el-input v-model="passwordform.name" readonly class="readonly" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="帐号：" prop="account">
              <el-input v-model="passwordform.account" readonly class="readonly" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="重置密码：" prop="password">
              <el-input v-model="passwordform.password" maxlength="64" autocomplete="off" placeholder="不填表示重置为Hntc@1234" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <span class="form-memo">密码包含：大写字母、小写字母、数字和特殊符号,长度8~20</span>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordVisible = false">取 消</el-button>
        <el-button type="primary" @click="savePassword">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PageTable from '@/views/components/PageTable.vue'
import TreeBox from '@/views/components/TreeBox.vue'
import Role from './role.vue'
const status = { '1': '在职', '5': '离职', '8': '禁用' }
const type = { '1': '内部用户', '2': '外部用户' }

export default {
  components: { PageTable, TreeBox, Role },
  data() {
    return {
      detailVisible: false,
      opt: null, // 操作类型
      qform: { account: null, name: null, dept: null },
      // 机构数据列表
      deptTree: [],
      form: { gender: '1' },
      rules: {
        dept: [{ required: true, message: '请选择部门' }],
        account: [{ required: true, message: '请输入帐号', trigger: 'blur' }],
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        type: [{ required: true, message: '请输入用户类型', trigger: 'blur' }]
      },
      passwordVisible: false,
      passwordform: {}
    }
  },
  created() {
    this.loadDeptTree()
    // 加载系统配置
    this.$store.dispatch('user/loadSystemConfig').catch(() => {
      console.warn('获取系统配置失败')
    })
  },
  mounted() {
    this.search()
  },
  methods: {
    isSuperAdminAccount(userRow) {
      // 判断是否为超级管理员账号
      const systemConfig = this.$store.getters.systemConfig
      if (systemConfig && systemConfig.superAdminAccount) {
        return systemConfig.superAdminAccount === userRow.account
      }
      return false
    },
    loadDeptTree() {
      this.$http('/sys/dept/treeByType/1').then(res => {
        this.deptTree = res
      }).catch(() => { this.$alert('加载机构树出错') })
    },
    fnStatus(r, c, v, i) {
      return status[v] || ''
    },
    fnType(r, c, v, i) {
      return type[v] || ''
    },
    search() {
      if (this.qform.dept === '0') this.qform.dept = null
      this.$refs.grid.search(this.qform)
    },
    clearValidate() {
      if (this.$refs.dataform) {
        this.$refs.dataform.clearValidate()
      } else {
        this.$nextTick(() => {
          this.$refs.dataform.clearValidate()
        })
      }
    },
    add() {
      this.opt = 'add'
      this.detailVisible = true
      this.clearValidate()
      this.form = { gender: '1' }
    },
    detail(data) {
      this.opt = 'update'
      this.detailVisible = true
      this.clearValidate()
      this.form = Object.assign({}, data)
    },
    assignRole(data) {
      this.$refs.role.show(data.id)
    },
    remove(data) {
      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
        .then(() => {
          this.$http({ url: '/sys/user/delete/' + data.id }).then(res => {
            if (res.code > 0) {
              this.$message.success('删除成功')
              this.detailVisible = false
              this.search()
            }
          })
        }).catch(() => {})
    },
    resetPassword(data) {
      this.passwordform = data
      this.passwordVisible = true
    },
    savePassword() {
      this.$http({ url: '/sys/user/resetPassword', data: { value: this.passwordform.id, text: this.passwordform.password }}).then(res => {
        if (res.code > 0) {
          this.$message.success('重置成功')
          this.passwordVisible = false
        }
      }).catch(() => {})
    },
    save() {
      this.$refs.dataform.validate(valid => {
        if (valid) {
          if (!this.form.type) this.form.type = '1'
          this.$http({ url: '/sys/user/' + this.opt, data: this.form }).then(res => {
            if (res.code > 0) {
              this.$message.success('提交成功')
              this.detailVisible = false
              this.search()
            }
          }).catch(() => {})
        }
      })
    }
  }
}
</script>
