{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\components\\PageTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\components\\PageTable.vue", "mtime": 1753320296361}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "name", "props", "path", "type", "String", "require", "default", "pageSize", "Number", "pageSizes", "Array", "layout", "paging", "Boolean", "query", "Object", "auto", "checkField", "data", "pi", "pz", "params", "rows", "total", "from", "to", "maxHeight", "loading", "watch", "mounted", "search", "methods", "setParams", "value", "setMaxHeight", "$refs", "grid", "doLayout", "handleSizeChange", "handleNumberChange", "arg", "a", "_this", "ps", "pageNumber", "argType", "_typeof", "empty", "url", "then", "res", "renderPage", "renderList", "$emit", "catch", "err", "console", "log", "_this2", "for<PERSON>ach", "r", "length", "getData", "getSelection", "selection", "getSelectionId", "field", "items", "ids", "i", "push"], "sources": ["src/views/components/PageTable.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"page-table-ctn\">\r\n    <!-- <el-popover v-if=\"columns.length > 0\" width=\"60\" trigger=\"click\">\r\n      <el-checkbox-group v-model=\"columns\">\r\n        <el-checkbox v-for=\"item in columns\" :key=\"item.index\" :label=\"item.title\" />\r\n      </el-checkbox-group>\r\n      <el-button slot=\"reference\" icon=\"el-icon-more\" circle style=\"margin-left: 100px;\"></el-button>\r\n    </el-popover> -->\r\n    <el-table ref=\"grid\" v-loading=\"loading\" :max-height=\"maxHeight\" :data=\"rows\" v-bind=\"$attrs\" v-on=\"$listeners\">\r\n      <slot></slot>\r\n    </el-table>\r\n    <el-footer v-if=\"paging\" class=\"footer\">\r\n      <div class=\"size-info\">\r\n        <span v-if=\"total > 1\">显示第 {{ from }} 条到第 {{ to }} 条的数据，</span> 共{{ total }} 条数据\r\n      </div>\r\n      <el-pagination\r\n        style=\"float:right\"\r\n        :layout=\"layout\"\r\n        :page-sizes=\"pageSizes\"\r\n        :current-page=\"pi\"\r\n        :page-size=\"pz\"\r\n        :total=\"total\"\r\n        v-bind=\"$attrs\"\r\n        @current-change=\"handleNumberChange\"\r\n        @size-change=\"handleSizeChange\"\r\n      ></el-pagination>\r\n    </el-footer>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\n\r\nimport request from '../../utils/request'\r\n\r\nexport default {\r\n  name: 'PageTable',\r\n  props: {\r\n    path: {\r\n      type: String,\r\n      require: true,\r\n      default: null\r\n    },\r\n    pageSize: {\r\n      type: Number,\r\n      default: 10\r\n    },\r\n    pageSizes: {\r\n      type: Array,\r\n      default: () => [10, 20, 30, 50, 100]\r\n    },\r\n    layout: {\r\n      type: String,\r\n      default: 'sizes, prev, pager, next, jumper'\r\n    },\r\n    paging: { // 是否分页，默认为true，即分页。（不分页时将每页条数设置最大。）\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    query: { // 初始化参数\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    auto: { // 自动查询\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    checkField: {\r\n      type: String,\r\n      default: null\r\n    }\r\n    // columns: {\r\n    //   type: Array,\r\n    //   default: () => []\r\n    // }\r\n  },\r\n  data() {\r\n    return {\r\n      pi: 1, // 页标\r\n      pz: this.pageSize, // 页长\r\n      params: this.query || {},\r\n      rows: [],\r\n      total: 0,\r\n      from: 0,\r\n      to: 0,\r\n      maxHeight: null,\r\n      loading: false\r\n    }\r\n  },\r\n  watch: {\r\n    // columnSelecteds(newArrayVal) {\r\n    //   console.log(newArrayVal)\r\n    //   // 计算为被选中的列标题数组\r\n    //   var nonSelecteds = this.columns.filter(item => newArrayVal.indexOf(item.index) === -1).map(item => item.index)\r\n    //   this.columns.filter(item => {\r\n    //     const isNonSelected = nonSelecteds.indexOf(item.index) !== -1\r\n    //     if (isNonSelected) {\r\n    //       // 隐藏未选中的列\r\n    //       item.visible = false\r\n    //       this.$nextTick(() => {\r\n    //         this.$refs.grid.doLayout()\r\n    //       })\r\n    //     } else {\r\n    //       // 显示已选中的列\r\n    //       item.visible = true\r\n    //       this.$nextTick(() => {\r\n    //         this.$refs.grid.doLayout()\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // }\r\n  },\r\n  mounted() {\r\n    if (this.auto) this.search()\r\n  },\r\n  methods: {\r\n    setParams(value) {\r\n      this.params = value || {}\r\n    },\r\n    setMaxHeight(value) {\r\n      this.maxHeight = value\r\n      this.$refs.grid.doLayout()\r\n    },\r\n    handleSizeChange(value) {\r\n      this.pz = value\r\n      this.search(1)\r\n    },\r\n    handleNumberChange(value) {\r\n      this.search(value)\r\n    },\r\n    search(arg, a) {\r\n      if (!this.path) return\r\n      const ps = { pageNumber: 1 }\r\n      const argType = typeof arg\r\n      if (argType === 'undefined') ps.pageNumber = 1\r\n      else if (argType === 'number') ps.pageNumber = arg\r\n      else if (argType === 'object') {\r\n        this.params = arg\r\n        if (typeof a === 'number') ps.pageNumber = a // 指定页码\r\n        if (typeof a === 'boolean') this.empty() // 查询前清空\r\n      } else ps.pageNumber = arg.pageNumber\r\n      this.pi = ps.pageNumber\r\n      if (this.paging) {\r\n        this.params.pageNumber = ps.pageNumber\r\n        this.params.pageSize = this.pz\r\n      }\r\n      this.loading = true\r\n      request({\r\n        url: this.path,\r\n        data: this.params\r\n      }).then(res => {\r\n        this.loading = false\r\n        if (this.paging) this.renderPage(res)\r\n        else this.renderList(res.rows ? res.rows : res)\r\n        this.$emit('loaded', res) // 加载数据返回\r\n      }).catch(err => {\r\n        this.loading = false\r\n        console.log(err)\r\n      })\r\n    },\r\n    empty() {\r\n      this.pi = 1\r\n      this.rows = []\r\n      this.total = 0\r\n      this.from = 0\r\n      this.to = 0\r\n    },\r\n    renderList(res) {\r\n      this.rows = res\r\n    },\r\n    renderPage(res) {\r\n      if (this.checkField) res.rows.forEach(r => { r[this.checkField] = false })\r\n      this.rows = res.rows\r\n      this.total = res.total\r\n      if (this.total > 0) {\r\n        this.from = (this.pi - 1) * this.pz + 1\r\n        this.to = this.from + (this.rows && this.rows.length ? this.rows.length - 1 : 0)\r\n      } else {\r\n        this.from = 0\r\n        this.to = 0\r\n      }\r\n    },\r\n    getData() {\r\n      return this.rows\r\n    },\r\n    getSelection() {\r\n      return this.$refs.grid.selection\r\n    },\r\n    getSelectionId(field) {\r\n      const items = this.$refs.grid.selection\r\n      if (!field) field = 'id'\r\n      const ids = []\r\n      for (let i = 0; i < items.length; i++) {\r\n        if (items[i][field]) ids.push(items[i][field])\r\n      }\r\n      return ids\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-table-ctn {\r\n  > .el-table {\r\n    width: '100%';\r\n    margin-bottom: 14px;\r\n    border: 1px solid #ebeef5;\r\n    border-bottom: unset;\r\n  }\r\n  > .footer {\r\n    height: 40px !important;\r\n    .size-info {\r\n      display: inline;\r\n      font-size: 12px;\r\n      color: #666666;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,OAAAA,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAJ,IAAA,EAAAK,MAAA;MACAF,OAAA;IACA;IACAG,SAAA;MACAN,IAAA,EAAAO,KAAA;MACAJ,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAK,MAAA;MACAR,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAM,MAAA;MAAA;MACAT,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;IACAQ,KAAA;MAAA;MACAX,IAAA,EAAAY,MAAA;MACAT,OAAA,WAAAA,SAAA;IACA;IACAU,IAAA;MAAA;MACAb,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;IACAW,UAAA;MACAd,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACA;IACA;IACA;IACA;EACA;EACAY,IAAA,WAAAA,KAAA;IACA;MACAC,EAAA;MAAA;MACAC,EAAA,OAAAb,QAAA;MAAA;MACAc,MAAA,OAAAP,KAAA;MACAQ,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,EAAA;MACAC,SAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC,OAAA,WAAAA,QAAA;IACA,SAAAb,IAAA,OAAAc,MAAA;EACA;EACAC,OAAA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MACA,KAAAZ,MAAA,GAAAY,KAAA;IACA;IACAC,YAAA,WAAAA,aAAAD,KAAA;MACA,KAAAP,SAAA,GAAAO,KAAA;MACA,KAAAE,KAAA,CAAAC,IAAA,CAAAC,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAL,KAAA;MACA,KAAAb,EAAA,GAAAa,KAAA;MACA,KAAAH,MAAA;IACA;IACAS,kBAAA,WAAAA,mBAAAN,KAAA;MACA,KAAAH,MAAA,CAAAG,KAAA;IACA;IACAH,MAAA,WAAAA,OAAAU,GAAA,EAAAC,CAAA;MAAA,IAAAC,KAAA;MACA,UAAAxC,IAAA;MACA,IAAAyC,EAAA;QAAAC,UAAA;MAAA;MACA,IAAAC,OAAA,GAAAC,OAAA,CAAAN,GAAA;MACA,IAAAK,OAAA,kBAAAF,EAAA,CAAAC,UAAA,UACA,IAAAC,OAAA,eAAAF,EAAA,CAAAC,UAAA,GAAAJ,GAAA,MACA,IAAAK,OAAA;QACA,KAAAxB,MAAA,GAAAmB,GAAA;QACA,WAAAC,CAAA,eAAAE,EAAA,CAAAC,UAAA,GAAAH,CAAA;QACA,WAAAA,CAAA,qBAAAM,KAAA;MACA,OAAAJ,EAAA,CAAAC,UAAA,GAAAJ,GAAA,CAAAI,UAAA;MACA,KAAAzB,EAAA,GAAAwB,EAAA,CAAAC,UAAA;MACA,SAAAhC,MAAA;QACA,KAAAS,MAAA,CAAAuB,UAAA,GAAAD,EAAA,CAAAC,UAAA;QACA,KAAAvB,MAAA,CAAAd,QAAA,QAAAa,EAAA;MACA;MACA,KAAAO,OAAA;MACA5B,OAAA;QACAiD,GAAA,OAAA9C,IAAA;QACAgB,IAAA,OAAAG;MACA,GAAA4B,IAAA,WAAAC,GAAA;QACAR,KAAA,CAAAf,OAAA;QACA,IAAAe,KAAA,CAAA9B,MAAA,EAAA8B,KAAA,CAAAS,UAAA,CAAAD,GAAA,OACAR,KAAA,CAAAU,UAAA,CAAAF,GAAA,CAAA5B,IAAA,GAAA4B,GAAA,CAAA5B,IAAA,GAAA4B,GAAA;QACAR,KAAA,CAAAW,KAAA,WAAAH,GAAA;MACA,GAAAI,KAAA,WAAAC,GAAA;QACAb,KAAA,CAAAf,OAAA;QACA6B,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA;IACA;IACAR,KAAA,WAAAA,MAAA;MACA,KAAA5B,EAAA;MACA,KAAAG,IAAA;MACA,KAAAC,KAAA;MACA,KAAAC,IAAA;MACA,KAAAC,EAAA;IACA;IACA2B,UAAA,WAAAA,WAAAF,GAAA;MACA,KAAA5B,IAAA,GAAA4B,GAAA;IACA;IACAC,UAAA,WAAAA,WAAAD,GAAA;MAAA,IAAAQ,MAAA;MACA,SAAAzC,UAAA,EAAAiC,GAAA,CAAA5B,IAAA,CAAAqC,OAAA,WAAAC,CAAA;QAAAA,CAAA,CAAAF,MAAA,CAAAzC,UAAA;MAAA;MACA,KAAAK,IAAA,GAAA4B,GAAA,CAAA5B,IAAA;MACA,KAAAC,KAAA,GAAA2B,GAAA,CAAA3B,KAAA;MACA,SAAAA,KAAA;QACA,KAAAC,IAAA,SAAAL,EAAA,aAAAC,EAAA;QACA,KAAAK,EAAA,QAAAD,IAAA,SAAAF,IAAA,SAAAA,IAAA,CAAAuC,MAAA,QAAAvC,IAAA,CAAAuC,MAAA;MACA;QACA,KAAArC,IAAA;QACA,KAAAC,EAAA;MACA;IACA;IACAqC,OAAA,WAAAA,QAAA;MACA,YAAAxC,IAAA;IACA;IACAyC,YAAA,WAAAA,aAAA;MACA,YAAA5B,KAAA,CAAAC,IAAA,CAAA4B,SAAA;IACA;IACAC,cAAA,WAAAA,eAAAC,KAAA;MACA,IAAAC,KAAA,QAAAhC,KAAA,CAAAC,IAAA,CAAA4B,SAAA;MACA,KAAAE,KAAA,EAAAA,KAAA;MACA,IAAAE,GAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,KAAA,CAAAN,MAAA,EAAAQ,CAAA;QACA,IAAAF,KAAA,CAAAE,CAAA,EAAAH,KAAA,GAAAE,GAAA,CAAAE,IAAA,CAAAH,KAAA,CAAAE,CAAA,EAAAH,KAAA;MACA;MACA,OAAAE,GAAA;IACA;EACA;AACA", "ignoreList": []}]}