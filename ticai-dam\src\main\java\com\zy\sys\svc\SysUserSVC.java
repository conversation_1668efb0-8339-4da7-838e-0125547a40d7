package com.zy.sys.svc;

import com.zy.core.EX;
import com.zy.model.KeyValues;
import com.zy.model.ListCheck;
import com.zy.model.Page;
import com.zy.sys.dao.SysUserDAO;
import com.zy.sys.orm.SysUser;
import com.zy.sys.vo.UserBaseVo;
import com.zy.sys.vo.UserVo;
import com.zy.util.CryptUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * <p>系统用户业务服务接口</p>
 */
@Service
public class SysUserSVC {

    /**
     * 系统用户数据访问接口
     **/
    @Resource
    private SysUserDAO dao;

    @Value("${zy.password:94A0B72F0615076C8824C3EAB1B2FFF9}")
    private String defaultPassword;

    @Value("${zy.super-admin-name}")
    private String superAdminAccount;

    public void add(SysUser item) {
        //检查帐号是否存在
        if (dao.countAccount(item.getAccount()) > 0) {
            throw new EX("该工号已经存在");
        }
        if (item.getPassword() == null) {
            String password = CryptUtils.decode(defaultPassword);
            item.setPassword(password);
        }
        item.setPassword(CryptUtils.md5(item.getPassword())); //转成MD5加密，不可逆
        dao.insert(item);
    }

    public void update(SysUser item) {
        dao.update(item);
    }

    public void resetPassword(String id, String password) {
        //判断密码长度和复杂度
        if (password == null || password.isEmpty()) password = CryptUtils.decode(defaultPassword);
        if (password.length() < 8 || password.length() > 20) {
            throw new EX("密码包含大写字母、小写字母、数字和特殊符号,长度8~20");
        }
        String regex = "(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$%^&*]).{8,}";
        boolean isValid = Pattern.matches(regex, password);
        if (!isValid) {
            throw new EX("密码包含大写字母、小写字母、数字和特殊符号,长度8~20");
        }
        dao.resetPassword(id, CryptUtils.md5(password));
    }

    /**
     * <p>
     * 数据分页
     * </p>
     *
     * @param page
     * @return
     */
    public List<UserVo> page(Page page) {
        return dao.page(page);
    }

    /**
     * <p>
     * 删除数据
     * </p>
     *
     * @param id
     */
    public void delete(String id) {
        // 超级管理员账号不允许删除
        if (superAdminAccount.equalsIgnoreCase(id)) throw new EX("该帐号不允许删除");
        dao.deleteRoleByUser(id);
        dao.delete(id);
    }

    public void updateStatus(String id, String status) {
        dao.updateStatus(id, status);
    }

    /**
     * <p>
     * 获取唯一数据
     * </p>
     *
     * @param id
     * @return UserVo
     */
    public UserVo findOne(String id) {
        return dao.findOne(id);
    }

    public List<String> findRoleIdByUser(String user) {
        return dao.findRoleIdByUser(user);
    }

    public List<ListCheck> findUserRole(String user) {
        return dao.findUserRole(user);
    }

    public void assignRole(KeyValues kvs) {
        dao.deleteRoleByUser(kvs.getKey());
        if (kvs.getValues() != null && kvs.getValues().length > 0) {
            for (String v : kvs.getValues()) {
                dao.insertUserRole(kvs.getKey(), v);
            }
        }
    }

    private boolean hasParent(Set<String> values, String v) {
        int len = v.length();
        if (len <= 2) return false;
        for (int i = 2; i < v.length(); i += 2) {
            if (values.contains(v.substring(0, i))) return true;
        }
        return false;
    }

    public List<UserBaseVo> listBase() {
        return dao.listBase();
    }

    public List<UserBaseVo> listByType(String type) {
        return dao.listByType(type);
    }


    /**
     * <p>
     * 数据分页
     * </p>
     *
     * @param page
     * @return
     */
    public List<UserVo> getMaintenancePage(Page page) {
        return dao.getMaintenancePage(page);
    }

}